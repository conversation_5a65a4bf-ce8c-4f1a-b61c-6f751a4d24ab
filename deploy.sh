#!/bin/bash

# 🚀 Multi-Vendor Ecommerce Deployment Script
# This script helps deploy all three parts of your application

echo "🚀 Starting deployment process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_success "All requirements met!"
}

# Install Vercel CLI if not present
install_vercel() {
    if ! command -v vercel &> /dev/null; then
        print_status "Installing Vercel CLI..."
        npm install -g vercel
        print_success "Vercel CLI installed!"
    else
        print_success "Vercel CLI already installed!"
    fi
}

# Deploy backend
deploy_backend() {
    print_status "Deploying backend..."
    cd backend
    
    # Install dependencies
    print_status "Installing backend dependencies..."
    npm install
    
    # Deploy to Vercel
    print_status "Deploying to Vercel..."
    vercel --prod --yes
    
    if [ $? -eq 0 ]; then
        print_success "Backend deployed successfully!"
        print_warning "Please note the backend URL and update frontend/dashboard environment variables"
    else
        print_error "Backend deployment failed!"
        exit 1
    fi
    
    cd ..
}

# Deploy frontend
deploy_frontend() {
    print_status "Deploying frontend..."
    cd frontend
    
    # Install dependencies
    print_status "Installing frontend dependencies..."
    npm install
    
    # Build the project
    print_status "Building frontend..."
    npm run build
    
    if [ $? -eq 0 ]; then
        print_success "Frontend build successful!"
        
        # Deploy to Vercel
        print_status "Deploying to Vercel..."
        vercel --prod --yes
        
        if [ $? -eq 0 ]; then
            print_success "Frontend deployed successfully!"
        else
            print_error "Frontend deployment failed!"
            exit 1
        fi
    else
        print_error "Frontend build failed!"
        exit 1
    fi
    
    cd ..
}

# Deploy dashboard
deploy_dashboard() {
    print_status "Deploying dashboard..."
    cd dashboard
    
    # Install dependencies
    print_status "Installing dashboard dependencies..."
    npm install
    
    # Build the project
    print_status "Building dashboard..."
    npm run build
    
    if [ $? -eq 0 ]; then
        print_success "Dashboard build successful!"
        
        # Deploy to Vercel
        print_status "Deploying to Vercel..."
        vercel --prod --yes
        
        if [ $? -eq 0 ]; then
            print_success "Dashboard deployed successfully!"
        else
            print_error "Dashboard deployment failed!"
            exit 1
        fi
    else
        print_error "Dashboard build failed!"
        exit 1
    fi
    
    cd ..
}

# Main deployment process
main() {
    echo "🚀 Multi-Vendor Ecommerce Deployment"
    echo "====================================="
    
    check_requirements
    install_vercel
    
    echo ""
    print_status "Starting deployment process..."
    echo ""
    
    # Ask user what to deploy
    echo "What would you like to deploy?"
    echo "1) Backend only"
    echo "2) Frontend only" 
    echo "3) Dashboard only"
    echo "4) All (Backend + Frontend + Dashboard)"
    echo "5) Frontend + Dashboard only"
    
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            deploy_backend
            ;;
        2)
            deploy_frontend
            ;;
        3)
            deploy_dashboard
            ;;
        4)
            deploy_backend
            deploy_frontend
            deploy_dashboard
            ;;
        5)
            deploy_frontend
            deploy_dashboard
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac
    
    echo ""
    print_success "🎉 Deployment completed!"
    echo ""
    print_warning "📝 Post-deployment checklist:"
    echo "1. Update environment variables with actual deployed URLs"
    echo "2. Test all functionality (auth, payments, chat)"
    echo "3. Update Stripe webhook URLs if using live keys"
    echo "4. Set up custom domains if needed"
    echo "5. Configure SSL certificates"
    echo ""
    print_status "Happy deploying! 🚀"
}

# Run main function
main
