const fs = require('fs');
const path = require('path');

// Paths to check
const publicImagesPath = path.join(__dirname, '../public/images');
const srcAssetsPath = path.join(__dirname, '../src/assets/images');

// Check if directory exists
const checkDirectory = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    console.error(`Directory does not exist: ${dirPath}`);
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`Created directory: ${dirPath}`);
    return false;
  }
  return true;
};

// Check if public/images exists
if (checkDirectory(publicImagesPath)) {
  console.log('✅ public/images directory exists');
}

// Check if src/assets/images exists
if (checkDirectory(srcAssetsPath)) {
  console.log('✅ src/assets/images directory exists');
}

// Check for specific required images
const requiredImages = [
  '/images/banner/shop.png'
];

requiredImages.forEach(imagePath => {
  const fullPath = path.join(__dirname, '../public', imagePath);
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ Missing required image: ${imagePath}`);
  } else {
    console.log(`✅ Found required image: ${imagePath}`);
  }
});

console.log('Image check completed');