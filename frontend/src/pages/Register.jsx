import React, { useEffect, useState } from 'react'
import Header from '../components/Header';
import Footer from '../components/Footer';
import { FaGoogle, FaEye, FaEyeSlash } from "react-icons/fa6";
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { customer_register, google_login, messageClear } from '../store/reducers/authReducer';
import toast from 'react-hot-toast';
import { FadeLoader } from 'react-spinners';

const Register = () => {
  const navigate = useNavigate()
  const { loader, errorMessage, successMessage, userInfo } = useSelector(state => state.auth)
  const dispatch = useDispatch()

  const [state, setState] = useState({
    name: '',
    email: '',
    password: ''
  })

  const [showPassword, setShowPassword] = useState(false)

  const inputHandle = (e) => {
    setState({
      ...state,
      [e.target.name]: e.target.value
    })
  }

  const register = (e) => {
    e.preventDefault()
    dispatch(customer_register(state))
  }

  // Google Login Handler (same as login)
  const handleGoogleLogin = () => {
    if (window.google) {
      window.google.accounts.id.prompt((notification) => {
        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
          window.google.accounts.id.renderButton(
            document.getElementById("google-signin-button"),
            {
              theme: "outline",
              size: "large",
              width: "100%",
              text: "signup_with"
            }
          );
        }
      });
    } else {
      toast.error('Google Sign-In not available. Please try again.');
    }
  }

  // Handle Google credential response
  const handleCredentialResponse = (response) => {
    try {
      const decoded = JSON.parse(atob(response.credential.split('.')[1]));

      const googleUserData = {
        email: decoded.email,
        name: decoded.name,
        googleId: decoded.sub,
        picture: decoded.picture
      };

      dispatch(google_login(googleUserData));
    } catch (error) {
      console.error('Google login error:', error);
      toast.error('Google sign-up failed. Please try again.');
    }
  }

  // Initialize Google Sign-In
  useEffect(() => {
    if (window.google && process.env.REACT_APP_GOOGLE_CLIENT_ID) {
      window.google.accounts.id.initialize({
        client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,
        callback: handleCredentialResponse
      });
    } else if (!process.env.REACT_APP_GOOGLE_CLIENT_ID) {
      console.warn('Google Client ID not configured');
    }
  }, [])

  useEffect(() => {
    if (successMessage) {
      toast.success(successMessage)
      dispatch(messageClear())
    }
    if (errorMessage) {
      toast.error(errorMessage)
      dispatch(messageClear())
    }
    if (userInfo) {
      navigate('/login')
    }
  }, [successMessage, errorMessage])

  return (
    <div>
      {
        loader && <div className='w-screen h-screen flex justify-center items-center fixed left-0 top-0 bg-[#********] z-[999]'>
          <FadeLoader />
        </div>
      }
      <Header />
      <div className='bg-slate-200 mt-4'>
        <div className='w-full justify-center items-center p-10'>
          <div className='grid grid-cols-2 w-[60%] mx-auto bg-white rounded-md md:w-[100%] md:grid-cols-1'>
            <div className='px-8 py-8'>
              <h2 className='text-center w-full text-xl text-slate-600 font-bold'>Register</h2>
              {/* form submit information */}
              <div>
                <form onSubmit={register} className='text-slate-600'>
                  <div className='flex flex-col gap-1 mb-2'>
                    <label htmlFor="name">Name</label>
                    <input className='w-full px-3 py-2 border border-slate-200 outline-none focus:border-green-500 rounded-md'
                      onChange={inputHandle}
                      value={state.name}
                      type="text"
                      name="name"
                      id="name"
                      placeholder='Input Name'
                      required />
                  </div>
                  <div className='flex flex-col gap-1 mb-2'>
                    <label htmlFor="email">Email</label>
                    <input className='w-full px-3 py-2 border border-slate-200 outline-none focus:border-green-500 rounded-md'
                      onChange={inputHandle}
                      value={state.email}
                      type="email"
                      name="email"
                      id="email"
                      placeholder='Input Email'
                      required />
                  </div>
                  <div className='flex flex-col gap-1 mb-2'>
                    <label htmlFor="password">Password</label>
                    <div className='relative'>
                      <input className='w-full px-3 py-2 pr-10 border border-slate-200 outline-none focus:border-green-500 rounded-md'
                        onChange={inputHandle}
                        value={state.password}
                        type={showPassword ? "text" : "password"}
                        name="password"
                        id="password"
                        placeholder='Password (min. 6 characters)'
                        required
                        minLength={6} />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700'
                      >
                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                      </button>
                    </div>
                  </div>
                  <button
                    disabled={loader}
                    className='px-8 w-full py-2 bg-[#059473] shadow-lg hover:shadow-green-500/40 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200'
                  >
                    {loader ? <FadeLoader color='#fff' size={20} /> : 'Create Account'}
                  </button>
                </form>

                <div className='flex justify-center items-center py-4'>
                  <div className='h-[1px] bg-slate-300 w-[95%]'></div>
                  <span className='px-3 text-slate-600 text-sm'>Or continue with</span>
                  <div className='h-[1px] bg-slate-300 w-[95%]'></div>
                </div>

                {/* Google Sign-In Button */}
                <button
                  onClick={handleGoogleLogin}
                  disabled={loader}
                  className='px-8 w-full py-2 bg-white border border-gray-300 shadow hover:shadow-md text-gray-700 rounded-md flex justify-center items-center gap-3 mb-4 transition-all duration-200 disabled:opacity-50'
                >
                  <FaGoogle className='text-red-500' />
                  <span>Continue with Google</span>
                </button>

                {/* Hidden Google Sign-In Button for fallback */}
                <div id="google-signin-button" className="hidden"></div>
              </div>
              <div className='text-center text-slate-600 pt-4'>
                <p>Already have an account? <Link className='text-green-500 hover:text-green-600 font-medium' to='/login'>Sign in</Link></p>
              </div>

              {/* Seller Registration Section */}
              <div className='mt-6 pt-4 border-t border-gray-200'>
                <p className='text-center text-sm text-gray-600 mb-3'>Want to sell on our platform?</p>
                <div className='grid grid-cols-2 gap-2'>
                  <a target='_blank' rel="noopener noreferrer" href="https://dashboard-multi-vendors-ecommerce.vercel.app/login">
                    <div className='px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md text-center text-sm transition-colors'>
                      Seller Login
                    </div>
                  </a>
                  <a target='_blank' rel="noopener noreferrer" href="https://dashboard-multi-vendors-ecommerce.vercel.app/register">
                    <div className='px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-md text-center text-sm transition-colors'>
                      Seller Register
                    </div>
                  </a>
                </div>
              </div>
            </div>
            <div className='w-full h-full block md:hidden'>
              <img src="/images/login.jpg" alt="" />
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}

export default Register