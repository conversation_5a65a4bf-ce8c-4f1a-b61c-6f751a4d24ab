import React, { useState } from 'react';
import { PaymentElement, LinkAuthenticationElement, useStripe, useElements } from '@stripe/react-stripe-js'

const CheckoutForm = ({ orderId }) => {
    // Store orderId for later use in confirmation
    if (orderId) {
        localStorage.setItem('orderId', orderId)
    }

    const stripe = useStripe()
    const elements = useElements()
    const [message, setMessage] = useState(null)
    const [isLoading, setIsLoading] = useState(false)

    const paymentElementOptions = {
        layout: 'tabs'
    }

    // Get the return URL based on environment
    const getReturnUrl = () => {
        const baseUrl = process.env.REACT_APP_FRONTEND_URL || window.location.origin;
        return `${baseUrl}/order/confirm`;
    }

    const submit = async (e) => {
        e.preventDefault()

        if (!stripe || !elements) {
            setMessage('Payment system is not ready. Please try again.')
            return
        }

        setIsLoading(true)
        setMessage(null)

        try {
            const { error } = await stripe.confirmPayment({
                elements,
                confirmParams: {
                    return_url: getReturnUrl()
                }
            })

            if (error) {
                // Payment failed
                if (error.type === 'card_error' || error.type === 'validation_error') {
                    setMessage(error.message)
                } else {
                    setMessage('An unexpected error occurred. Please try again.')
                }
            }
            // If no error, the user will be redirected to the return_url
        } catch (err) {
            console.error('Payment confirmation error:', err)
            setMessage('Payment processing failed. Please try again.')
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <form onSubmit={submit} id='payment-form' className='space-y-4'>
            <LinkAuthenticationElement id='link-authentication-element' />
            <PaymentElement id='payment-element' options={paymentElementOptions} />

            {message && (
                <div className='p-3 bg-red-100 border border-red-400 text-red-700 rounded'>
                    {message}
                </div>
            )}

            <button
                disabled={isLoading || !stripe || !elements}
                id='submit'
                className={`w-full px-10 py-3 rounded-sm text-white font-medium ${
                    isLoading || !stripe || !elements
                        ? 'bg-gray-500 cursor-not-allowed'
                        : 'bg-green-700 hover:shadow-green-700/30 hover:shadow-lg'
                }`}
            >
                <span id='button-text'>
                    {isLoading ? 'Processing...' : 'Pay Now'}
                </span>
            </button>
        </form>
    );
};

export default CheckoutForm;