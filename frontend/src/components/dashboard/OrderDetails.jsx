import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useParams } from 'react-router-dom';
import { get_order_details } from '../../store/reducers/orderReducer';
import ShippingProgress from '../ShippingProgress';

const OrderDetails = () => {

    const { orderId } = useParams()
    const dispatch = useDispatch()

    const { userInfo } = useSelector(state => state.auth)
    const { myOrder } = useSelector(state => state.order)

    useEffect(() => {
        dispatch(get_order_details(orderId))
    }, [orderId, dispatch])

    // Show loading state if order is not loaded yet
    if (!myOrder || !myOrder._id) {
        return (
            <div className='flex justify-center items-center h-64'>
                <div className='text-center'>
                    <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4'></div>
                    <p className='text-slate-600'>Loading order details...</p>
                </div>
            </div>
        );
    }

    return (
        <div className='space-y-6'>
            {/* Shipping Progress */}
            <ShippingProgress
                status={myOrder.delivery_status || 'placed'}
                orderDate={myOrder.date}
            />

            {/* Order Details */}
            <div className='bg-white p-6 rounded-lg shadow-sm'>
                <div className='border-b border-gray-200 pb-4 mb-6'>
                    <h2 className='text-xl font-semibold text-slate-700 mb-2'>Order Details</h2>
                    <div className='flex flex-wrap items-center gap-4 text-sm text-slate-600'>
                        <span className='font-medium'>Order ID: #{myOrder._id}</span>
                        <span>•</span>
                        <span>Placed on {myOrder.date ? new Date(myOrder.date).toLocaleDateString() : 'N/A'}</span>
                    </div>
                </div>
                <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
                {/* Shipping Information */}
                <div className='bg-gray-50 p-4 rounded-lg'>
                    <h3 className='text-lg font-semibold text-slate-700 mb-3 flex items-center'>
                        <span className='mr-2'>🚚</span>
                        Shipping Information
                    </h3>
                    <div className='space-y-2'>
                        <div>
                            <span className='text-sm font-medium text-slate-600'>Deliver To:</span>
                            <p className='text-slate-800 font-semibold'>{myOrder.shippingInfo?.name || 'N/A'}</p>
                        </div>
                        <div>
                            <span className='text-sm font-medium text-slate-600'>Address:</span>
                            <p className='text-slate-700'>
                                {myOrder.shippingInfo?.address || ''} {myOrder.shippingInfo?.city || ''} {myOrder.shippingInfo?.province || ''}
                            </p>
                        </div>
                        <div>
                            <span className='text-sm font-medium text-slate-600'>Email:</span>
                            <p className='text-slate-700'>{userInfo?.email || 'N/A'}</p>
                        </div>
                    </div>
                </div>

                {/* Order Summary */}
                <div className='bg-gray-50 p-4 rounded-lg'>
                    <h3 className='text-lg font-semibold text-slate-700 mb-3 flex items-center'>
                        <span className='mr-2'>💰</span>
                        Order Summary
                    </h3>
                    <div className='space-y-3'>
                        <div className='flex justify-between items-center'>
                            <span className='text-slate-600'>Total Amount:</span>
                            <span className='text-xl font-bold text-green-600'>${myOrder.price || 0}</span>
                        </div>
                        <div className='text-xs text-slate-500'>*Including shipping fees</div>

                        <div className='pt-3 border-t border-gray-200 space-y-2'>
                            <div className='flex justify-between items-center'>
                                <span className='text-sm font-medium text-slate-600'>Payment Status:</span>
                                <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                                    myOrder.payment_status === 'paid' || myOrder.payment_status === 'paid-cod'
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-red-100 text-red-800'
                                }`}>
                                    {myOrder.payment_status || 'pending'}
                                </span>
                            </div>
                            <div className='flex justify-between items-center'>
                                <span className='text-sm font-medium text-slate-600'>Delivery Status:</span>
                                <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                                    myOrder.delivery_status === 'delivered'
                                        ? 'bg-green-100 text-green-800'
                                        : myOrder.delivery_status === 'shipped'
                                            ? 'bg-blue-100 text-blue-800'
                                            : myOrder.delivery_status === 'cancelled'
                                                ? 'bg-red-100 text-red-800'
                                                : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                    {myOrder.delivery_status || 'pending'}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Order Products */}
            <div className='bg-white p-6 rounded-lg shadow-sm'>
                <h3 className='text-xl font-semibold text-slate-700 mb-4 flex items-center'>
                    <span className='mr-2'>📦</span>
                    Order Items ({myOrder.products?.length || 0})
                </h3>
                <div className='space-y-4'>
                    {myOrder.products?.length > 0 ? (
                        myOrder.products.map((p, i) => (
                            <div key={i} className='border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow'>
                                <div className='flex gap-4 items-start'>
                                    <div className='flex-shrink-0'>
                                        <img
                                            className='w-16 h-16 object-cover rounded-lg border'
                                            src={p.images?.[0] || '/placeholder-image.jpg'}
                                            alt={p.name || 'Product'}
                                        />
                                    </div>
                                    <div className='flex-grow'>
                                        <div className='flex justify-between items-start'>
                                            <div>
                                                <h4 className='font-semibold text-slate-800 mb-1'>{p.name || 'Unknown Product'}</h4>
                                                <div className='text-sm text-slate-600 space-y-1'>
                                                    <p><span className='font-medium'>Brand:</span> {p.brand || 'N/A'}</p>
                                                    <p><span className='font-medium'>Quantity:</span> {p.quantity || 1}</p>
                                                </div>
                                            </div>
                                            <div className='text-right'>
                                                <div className='text-lg font-bold text-green-600'>
                                                    ${p.discount > 0
                                                        ? (p.price - Math.floor((p.price * p.discount) / 100)).toFixed(2)
                                                        : p.price?.toFixed(2) || '0.00'
                                                    }
                                                </div>
                                                {p.discount > 0 && (
                                                    <div className='text-sm text-slate-500'>
                                                        <span className='line-through'>${p.price?.toFixed(2)}</span>
                                                        <span className='ml-2 text-red-500 font-semibold'>-{p.discount}%</span>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className='text-center py-8 text-slate-500'>
                            <div className='text-4xl mb-2'>📦</div>
                            <p>No products found in this order</p>
                        </div>
                    )}
                </div>
            </div>
            </div>
        </div>
    );
};
export default OrderDetails;