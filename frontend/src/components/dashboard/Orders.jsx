import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link, useNavigate } from 'react-router-dom'
import { get_orders } from '../../store/reducers/orderReducer'

const Orders = () => {

    const [state, setState] = useState('all')

    const navigate = useNavigate()
    const dispatch = useDispatch()
    const { userInfo } = useSelector(state => state.auth)
    const { myOrders } = useSelector(state => state.order)

    useEffect(() => {
        dispatch(get_orders({ status: state, customerId: userInfo.id }))
    }, [state])

    const redirect = (ord) => {
        const items = ord.products?.reduce((total, product) => total + product.quantity, 0) || 0;

        navigate('/payment', {
            state: {
                price: ord.price,
                items,
                orderId: ord._id
            }
        });
    };

    return (
        <div className='bg-white p-4 rounded-md'>
            <div className='flex justify-between items-center'>
                <h2 className='text-xl font-semibold text-slate-600'>My Orders </h2>
                <select className='outline-none px-3 py-1 border rounded-md text-slate-600' value={state} onChange={(e) => setState(e.target.value)}>
                    <option value="all">--Order status--</option>
                    <option value="placed">Placed</option>
                    <option value="pending">Processing</option>
                    <option value="warehouse">In Warehouse</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
            <div className='pt-4'>
                <div className='relative overflow-x-auto rounded-md'>
                    <table className='w-full text-sm text-left text-gray-500'>
                        <thead className='text-xs text-gray-700 uppercase bg-gray-200'>
                            <tr>
                                <th scope='col' className='px-6 py-3'>Order Id</th>
                                <th scope='col' className='px-6 py-3'>Price</th>
                                <th scope='col' className='px-6 py-3'>Payment Status</th>
                                <th scope='col' className='px-6 py-3'>Order Status</th>
                                <th scope='col' className='px-6 py-3'>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {
                                myOrders.map((o, i) => {
                                    // Helper function to get status color
                                    const getStatusColor = (status) => {
                                        switch(status) {
                                            case 'paid':
                                            case 'paid-cod':
                                            case 'delivered':
                                                return 'bg-green-100 text-green-800';
                                            case 'shipped':
                                            case 'warehouse':
                                                return 'bg-blue-100 text-blue-800';
                                            case 'pending':
                                            case 'placed':
                                                return 'bg-yellow-100 text-yellow-800';
                                            case 'cancelled':
                                                return 'bg-red-100 text-red-800';
                                            default:
                                                return 'bg-gray-100 text-gray-800';
                                        }
                                    };

                                    return (
                                        <tr key={i} className='bg-white border-b hover:bg-gray-50'>
                                            <td scope='row' className='px-6 py-4 font-medium whitespace-nowrap'>#{o._id}</td>
                                            <td scope='row' className='px-6 py-4 font-medium whitespace-nowrap'>${o.price}</td>
                                            <td scope='row' className='px-6 py-4 font-medium whitespace-nowrap'>
                                                <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(o.payment_status)}`}>
                                                    {o.payment_status}
                                                </span>
                                            </td>
                                            <td scope='row' className='px-6 py-4 font-medium whitespace-nowrap'>
                                                <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(o.delivery_status)}`}>
                                                    {o.delivery_status}
                                                </span>
                                            </td>
                                            <td scope='row' className='px-6 py-4 font-medium whitespace-nowrap'>
                                                <Link to={`/dashboard/order/details/${o._id}`}>
                                                    <span className='bg-green-200 text-green-800 text-sm font-semibold mr-2 px-3 py-1 rounded hover:bg-green-300 transition-colors'>
                                                        View Details
                                                    </span>
                                                </Link>
                                                {
                                                    (o.payment_status !== 'paid' && o.payment_status !== 'paid-cod') &&
                                                    <span
                                                        onClick={() => redirect(o)}
                                                        className='bg-blue-200 text-blue-800 text-sm font-semibold mr-2 px-3 py-1 rounded cursor-pointer hover:bg-blue-300 transition-colors'
                                                    >
                                                        Pay Now
                                                    </span>
                                                }
                                            </td>
                                        </tr>
                                    );
                                })
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    )
}

export default Orders