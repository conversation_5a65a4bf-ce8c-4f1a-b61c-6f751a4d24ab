import React, { useEffect, useRef, useState } from "react";
import { AiOutlineMessage, AiOutlinePlus } from "react-icons/ai";
import { <PERSON>r<PERSON><PERSON><PERSON> } from "react-icons/gr";
import { IoSend } from "react-icons/io5";
import { useDispatch, useSelector } from "react-redux";
import { Link, useParams } from "react-router-dom";
import {
    add_friend,
    messageClear,
    send_message,
    updateMessage,
} from "../../store/reducers/chatReducer";
import toast from "react-hot-toast";
import { FaList } from "react-icons/fa";
import { initializeSocket, getSocket, isSocketConnected, emitSocketEvent, onSocketEvent } from "../../utils/socket";

const Chat = () => {
    const scrollRef = useRef();
    const dispatch = useDispatch();
    const { sellerId } = useParams();
    const { userInfo } = useSelector((state) => state.auth);
    const { fb_messages, currentFd, my_friends, successMessage } = useSelector(
        (state) => state.chat
    );
    const [text, setText] = useState("");
    const [receverMessage, setReceverMessage] = useState("");
    const [activeSeller, setActiveSeller] = useState([]);
    const [show, setShow] = useState(false);
    const [socketConnected, setSocketConnected] = useState(false);

    // Initialize socket connection
    useEffect(() => {
        const socket = initializeSocket();

        const handleConnect = () => {
            console.log('Socket connected');
            setSocketConnected(true);
        };

        const handleDisconnect = () => {
            console.log('Socket disconnected');
            setSocketConnected(false);
        };

        const handleConnectError = (error) => {
            console.error('Socket connection error:', error);
            setSocketConnected(false);
        };

        // Set up event listeners
        const cleanupConnect = onSocketEvent('connect', handleConnect);
        const cleanupDisconnect = onSocketEvent('disconnect', handleDisconnect);
        const cleanupError = onSocketEvent('connect_error', handleConnectError);

        // Set initial connection state
        setSocketConnected(isSocketConnected());

        return () => {
            cleanupConnect();
            cleanupDisconnect();
            cleanupError();
        };
    }, []);

    // Add user when socket is connected and userInfo is available
    useEffect(() => {
        if (socketConnected && userInfo?.id) {
            emitSocketEvent("add_user", userInfo.id, userInfo);
        }
    }, [socketConnected, userInfo]);

    // Load friend data when sellerId changes
    useEffect(() => {
        if (userInfo?.id) {
            dispatch(
                add_friend({
                    sellerId: sellerId || "",
                    userId: userInfo.id,
                })
            );
        }
    }, [sellerId, userInfo?.id, dispatch]);

    const send = () => {
        if (text.trim() && userInfo?.id && sellerId) {
            dispatch(
                send_message({
                    userId: userInfo.id,
                    text: text.trim(),
                    sellerId,
                    name: userInfo.name,
                })
            );
            setText("");
        }
    };

    // Handle socket events
    useEffect(() => {
        const handleSellerMessage = (msg) => {
            setReceverMessage(msg);
        };

        const handleActiveSeller = (sellers) => {
            setActiveSeller(sellers);
        };

        const cleanupSellerMessage = onSocketEvent("seller_message", handleSellerMessage);
        const cleanupActiveSeller = onSocketEvent("activeSeller", handleActiveSeller);

        // Cleanup listeners
        return () => {
            cleanupSellerMessage();
            cleanupActiveSeller();
        };
    }, []);

    // Handle successful message send
    useEffect(() => {
        if (successMessage && fb_messages.length > 0) {
            const lastMessage = fb_messages[fb_messages.length - 1];
            emitSocketEvent("send_customer_message", lastMessage);
            dispatch(messageClear());
        }
    }, [successMessage, fb_messages, dispatch]);

    // Handle received messages
    useEffect(() => {
        if (receverMessage && userInfo?.id) {
            if (
                sellerId === receverMessage.senderId &&
                userInfo.id === receverMessage.receverId
            ) {
                dispatch(updateMessage(receverMessage));
            } else {
                toast.success(`${receverMessage.senderName} sent a message`);
                dispatch(messageClear());
            }
        }
    }, [receverMessage, sellerId, userInfo?.id, dispatch]);

    // Auto-scroll to bottom when new messages arrive
    useEffect(() => {
        scrollRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [fb_messages]);

    // Handle Enter key press for sending messages
    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            send();
        }
    };

    return (
        <div className="bg-white p-3 rounded-md">
            <div className="w-full flex">
                <div className={`w-[230px] md-lg:absolute bg-white md-lg:h-[450px] -left-[350px] ${show ? "-left-0" : "left-[5px]"}`}>
                    <div className="flex justify-center gap-3 items-center text-slate-600 text-xl h-[50px]">
                        <span>
                            <AiOutlineMessage />
                        </span>
                        <span>Message</span>
                    </div>
                    <div className="w-full flex flex-col text-slate-600 py-4 h-[400px] pr-3">
                        {my_friends.map((f, i) => (
                            <Link
                                to={`/dashboard/chat/${f.fdId}`}
                                key={i}
                                className={`flex gap-2 justify-start items-center pl-2 py-[5px]`}
                            >
                                <div className="w-[30px] h-[30px] rounded-full relative">
                                    {activeSeller.some((c) => c.sellerId === f.fdId) && (
                                        <div className="w-[10px] h-[10px] rounded-full bg-green-500 absolute right-0 bottom-0"></div>
                                    )}
                                    <img src={f.image} alt="" />
                                </div>
                                <span>{f.name}</span>
                            </Link>
                        ))}
                    </div>
                </div>

                <div className="w-[calc(100%-230px)] md-lg:w-full">
                    {currentFd ? (
                        <div className="w-full h-full">
                            <div className="flex justify-between gap-3 items-center text-slate-600 text-xl h-[50px]">
                                <div className="flex gap-2 items-center">
                                    <div className="w-[30px] h-[30px] rounded-full relative">
                                        {activeSeller.some(
                                            (c) => c.sellerId === currentFd.fdId
                                        ) && (
                                                <div className="w-[10px] h-[10px] rounded-full bg-green-500 absolute right-0 bottom-0"></div>
                                            )}
                                        <img src={currentFd.image} alt={currentFd.name} />
                                    </div>
                                    <div className="flex flex-col">
                                        <span>{currentFd.name}</span>
                                        <span className="text-xs text-gray-500">
                                            {socketConnected ? 'Connected' : 'Connecting...'}
                                        </span>
                                    </div>
                                </div>

                                <div
                                    onClick={() => setShow(!show)}
                                    className="w-[35px] h-[35px] mb-2 hidden md-lg:flex cursor-pointer rounded-sm justify-center items-center bg-sky-500 text-white"
                                >
                                    <FaList />
                                </div>
                            </div>
                            <div className="h-[400px] w-full bg-slate-100 p-3 rounded-md">
                                <div className="w-full h-full overflow-y-auto flex flex-col gap-3">
                                    {fb_messages.map((m, i) => {
                                        if (currentFd?.fdId !== m.receverId) {
                                            return (
                                                <div
                                                    ref={scrollRef}
                                                    key={i}
                                                    className="w-full flex gap-2 justify-start items-center text-[14px]"
                                                >
                                                    <img
                                                        className="w-[30px] h-[30px] "
                                                        src="/images/user.png"
                                                        alt=""
                                                    />
                                                    <div className="p-2 bg-purple-500 text-white rounded-md">
                                                        <span>{m.message}</span>
                                                    </div>
                                                </div>
                                            );
                                        } else {
                                            return (
                                                <div
                                                    ref={scrollRef}
                                                    key={i}
                                                    className="w-full flex gap-2 justify-end items-center text-[14px]"
                                                >
                                                    <img
                                                        className="w-[30px] h-[30px] "
                                                        src="/images/user.png"
                                                        alt=""
                                                    />
                                                    <div className="p-2 bg-cyan-500 text-white rounded-md">
                                                        <span>{m.message}</span>
                                                    </div>
                                                </div>
                                            );
                                        }
                                    })}
                                </div>
                            </div>
                            <div className="flex p-2 justify-between items-center w-full">
                                <div className="w-[40px] h-[40px] border p-2 justify-center items-center flex rounded-full">
                                    <label className="cursor-pointer" htmlFor="">
                                        <AiOutlinePlus />
                                    </label>
                                    <input className="hidden" type="file" />
                                </div>
                                <div className="border h-[40px] p-0 ml-2 w-[calc(100%-90px)] rounded-full relative">
                                    <input
                                        value={text}
                                        onChange={(e) => setText(e.target.value)}
                                        onKeyPress={handleKeyPress}
                                        type="text"
                                        placeholder="Type a message..."
                                        className="w-full rounded-full h-full outline-none p-3"
                                        disabled={!socketConnected}
                                    />
                                    <div className="text-2xl right-2 top-2 absolute cursor-auto">
                                        <span>
                                            <GrEmoji />
                                        </span>
                                    </div>
                                </div>
                                <div className="w-[40px] p-2 justify-center items-center rounded-full">
                                    <div
                                        onClick={send}
                                        className={`text-2xl cursor-pointer ${
                                            !socketConnected || !text.trim()
                                                ? 'text-gray-400 cursor-not-allowed'
                                                : 'text-blue-500 hover:text-blue-600'
                                        }`}
                                        title={!socketConnected ? 'Connecting...' : 'Send message'}
                                    >
                                        <IoSend />
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div
                            onClick={() => setShow(true)}
                            className="w-full h-[400px] flex justify-center items-center text-lg ont-bold text-slate-600"
                        >
                            <span>Select Seller</span>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Chat;
