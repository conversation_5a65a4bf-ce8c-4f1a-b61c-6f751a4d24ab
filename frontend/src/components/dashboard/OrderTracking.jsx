import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { get_orders } from '../../store/reducers/orderReducer';
import ShippingProgress from '../ShippingProgress';
import { Link } from 'react-router-dom';
import { FaSearch, FaEye } from 'react-icons/fa';

const OrderTracking = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredOrders, setFilteredOrders] = useState([]);
    
    const dispatch = useDispatch();
    const { userInfo } = useSelector(state => state.auth);
    const { myOrders } = useSelector(state => state.order);

    useEffect(() => {
        if (userInfo) {
            dispatch(get_orders({ status: 'all', customerId: userInfo.id }));
        }
    }, [userInfo, dispatch]);

    useEffect(() => {
        if (searchTerm) {
            const filtered = myOrders.filter(order => 
                order._id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                order.delivery_status.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilteredOrders(filtered);
        } else {
            setFilteredOrders(myOrders);
        }
    }, [searchTerm, myOrders]);

    const getStatusIcon = (status) => {
        switch(status) {
            case 'delivered': return '✅';
            case 'shipped': return '🚚';
            case 'warehouse': return '📦';
            case 'pending': return '⏳';
            case 'placed': return '📋';
            case 'cancelled': return '❌';
            default: return '📋';
        }
    };

    const getStatusColor = (status) => {
        switch(status) {
            case 'delivered': return 'text-green-600 bg-green-50';
            case 'shipped': return 'text-blue-600 bg-blue-50';
            case 'warehouse': return 'text-purple-600 bg-purple-50';
            case 'pending': return 'text-yellow-600 bg-yellow-50';
            case 'placed': return 'text-gray-600 bg-gray-50';
            case 'cancelled': return 'text-red-600 bg-red-50';
            default: return 'text-gray-600 bg-gray-50';
        }
    };

    return (
        <div className='bg-white p-6 rounded-lg shadow-sm'>
            <div className='flex justify-between items-center mb-6'>
                <h2 className='text-2xl font-semibold text-slate-700'>Order Tracking</h2>
                <div className='relative'>
                    <FaSearch className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400' />
                    <input
                        type="text"
                        placeholder="Search by Order ID or Status"
                        className='pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent'
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
            </div>

            {filteredOrders.length === 0 ? (
                <div className='text-center py-12'>
                    <div className='text-6xl mb-4'>📦</div>
                    <h3 className='text-xl font-semibold text-slate-600 mb-2'>No Orders Found</h3>
                    <p className='text-slate-500'>
                        {searchTerm ? 'No orders match your search criteria' : 'You haven\'t placed any orders yet'}
                    </p>
                </div>
            ) : (
                <div className='space-y-6'>
                    {filteredOrders.map((order, index) => (
                        <div key={index} className='border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow'>
                            {/* Order Header */}
                            <div className='flex justify-between items-start mb-4'>
                                <div>
                                    <h3 className='text-lg font-semibold text-slate-700'>
                                        Order #{order._id}
                                    </h3>
                                    <p className='text-sm text-slate-500'>
                                        Placed on {new Date(order.date).toLocaleDateString()}
                                    </p>
                                </div>
                                <div className='flex items-center space-x-3'>
                                    <div className={`flex items-center px-3 py-1 rounded-full ${getStatusColor(order.delivery_status)}`}>
                                        <span className='mr-2'>{getStatusIcon(order.delivery_status)}</span>
                                        <span className='text-sm font-semibold capitalize'>
                                            {order.delivery_status}
                                        </span>
                                    </div>
                                    <Link 
                                        to={`/dashboard/order/details/${order._id}`}
                                        className='flex items-center px-3 py-1 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors'
                                    >
                                        <FaEye className='mr-1' />
                                        <span className='text-sm font-semibold'>View</span>
                                    </Link>
                                </div>
                            </div>

                            {/* Order Summary */}
                            <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
                                <div className='bg-gray-50 p-3 rounded-lg'>
                                    <p className='text-sm text-slate-500'>Total Amount</p>
                                    <p className='text-lg font-semibold text-slate-700'>${order.price}</p>
                                </div>
                                <div className='bg-gray-50 p-3 rounded-lg'>
                                    <p className='text-sm text-slate-500'>Payment Status</p>
                                    <p className={`text-sm font-semibold capitalize ${
                                        order.payment_status === 'paid' || order.payment_status === 'paid-cod' 
                                            ? 'text-green-600' 
                                            : 'text-red-600'
                                    }`}>
                                        {order.payment_status}
                                    </p>
                                </div>
                                <div className='bg-gray-50 p-3 rounded-lg'>
                                    <p className='text-sm text-slate-500'>Items</p>
                                    <p className='text-lg font-semibold text-slate-700'>
                                        {order.products?.reduce((total, product) => total + product.quantity, 0) || 0}
                                    </p>
                                </div>
                            </div>

                            {/* Shipping Progress */}
                            <div className='bg-gray-50 p-4 rounded-lg'>
                                <ShippingProgress
                                    status={order.delivery_status || 'placed'}
                                    orderDate={order.date}
                                />
                            </div>

                            {/* Quick Actions */}
                            <div className='flex justify-end mt-4 space-x-3'>
                                {(order.payment_status !== 'paid' && order.payment_status !== 'paid-cod') && (
                                    <button className='px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors'>
                                        Pay Now
                                    </button>
                                )}
                                {order.delivery_status === 'delivered' && (
                                    <button className='px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors'>
                                        Rate Order
                                    </button>
                                )}
                                {(order.delivery_status === 'placed' || order.delivery_status === 'pending') && (
                                    <button className='px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors'>
                                        Cancel Order
                                    </button>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default OrderTracking;
