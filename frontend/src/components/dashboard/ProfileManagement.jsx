import React, { useEffect, useState } from 'react';
import { <PERSON>a<PERSON><PERSON>, <PERSON>aLock, FaEdit, FaSave, FaTimes, FaEnvelope, FaCalendar, FaGoogle } from "react-icons/fa";
import { useDispatch, useSelector } from 'react-redux';
import { customer_change_password, messageClear } from '../../store/reducers/authReducer';
import toast from 'react-hot-toast';
import { PropagateLoader } from 'react-spinners';
import { overrideStyle } from '../../utils/utils';

const ProfileManagement = () => {
    const dispatch = useDispatch();
    const { userInfo, loader, successMessage, errorMessage } = useSelector(state => state.auth);

    // State for different sections
    const [activeTab, setActiveTab] = useState('profile');

    // Password change state
    const [passwordData, setPasswordData] = useState({
        old_password: '',
        new_password: '',
        confirm_password: ''
    });

    // Handle success/error messages
    useEffect(() => {
        if (successMessage) {
            toast.success(successMessage);
            dispatch(messageClear());
            // Reset password form on success
            if (successMessage.includes('Password')) {
                setPasswordData({
                    old_password: '',
                    new_password: '',
                    confirm_password: ''
                });
            }
        }
        if (errorMessage) {
            toast.error(errorMessage);
            dispatch(messageClear());
        }
    }, [successMessage, errorMessage, dispatch]);

    // Handle password change
    const handlePasswordChange = (e) => {
        e.preventDefault();
        
        // Validation
        if (!passwordData.old_password || !passwordData.new_password || !passwordData.confirm_password) {
            toast.error('All password fields are required');
            return;
        }
        if (passwordData.new_password !== passwordData.confirm_password) {
            toast.error('New passwords do not match');
            return;
        }
        if (passwordData.new_password.length < 6) {
            toast.error('New password must be at least 6 characters');
            return;
        }

        dispatch(customer_change_password({
            email: userInfo.email,
            info: {
                old_password: passwordData.old_password,
                new_password: passwordData.new_password,
                confirm_password: passwordData.confirm_password
            }
        }));
    };

    const tabs = [
        { id: 'profile', label: 'Profile Info', icon: FaUser },
        { id: 'password', label: 'Change Password', icon: FaLock }
    ];

    // Format date for display
    const formatDate = (timestamp) => {
        if (!timestamp) return 'Not available';
        return new Date(timestamp * 1000).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <div className='p-4 bg-white'>
            <div className='w-full bg-white rounded-md shadow-sm border'>
                {/* Header */}
                <div className='p-6 border-b border-gray-200'>
                    <h1 className='text-2xl font-semibold text-slate-600'>Profile Management</h1>
                    <p className='text-sm text-gray-500 mt-1'>Manage your profile information and settings</p>
                </div>

                {/* Tab Navigation */}
                <div className='flex border-b border-gray-200'>
                    {tabs.map((tab) => {
                        const Icon = tab.icon;
                        return (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`flex items-center gap-2 px-6 py-3 transition-colors ${
                                    activeTab === tab.id
                                        ? 'bg-green-50 border-b-2 border-green-500 text-green-600'
                                        : 'text-gray-600 hover:bg-gray-50'
                                }`}
                            >
                                <Icon className='text-sm' />
                                {tab.label}
                            </button>
                        );
                    })}
                </div>

                {/* Tab Content */}
                <div className='p-6'>
                    {activeTab === 'profile' && (
                        <div className='max-w-2xl mx-auto'>
                            {/* Profile Information */}
                            <div className='bg-gray-50 rounded-lg p-6 mb-6'>
                                <h3 className='text-lg font-semibold mb-4 flex items-center gap-2 text-slate-600'>
                                    <FaUser />
                                    Personal Information
                                </h3>
                                
                                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                                    {/* Profile Avatar */}
                                    <div className='md:col-span-2 flex justify-center mb-4'>
                                        <div className='relative'>
                                            {userInfo?.picture ? (
                                                <img
                                                    src={userInfo.picture}
                                                    alt="Profile"
                                                    className='w-24 h-24 rounded-full object-cover border-4 border-green-500'
                                                />
                                            ) : (
                                                <div className='w-24 h-24 rounded-full bg-green-500 flex items-center justify-center text-white text-2xl font-bold'>
                                                    {userInfo?.name ? userInfo.name.charAt(0).toUpperCase() : 'U'}
                                                </div>
                                            )}
                                            <div className='absolute bottom-0 right-0 w-6 h-6 bg-green-400 rounded-full border-2 border-white flex items-center justify-center'>
                                                {userInfo?.method === 'google' && (
                                                    <FaGoogle className='text-xs text-white' />
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Name */}
                                    <div className='space-y-2'>
                                        <label className='block text-sm font-medium text-gray-700'>
                                            <FaUser className='inline mr-2' />
                                            Full Name
                                        </label>
                                        <div className='p-3 bg-white border border-gray-200 rounded-md'>
                                            <span className='text-gray-900'>{userInfo?.name || 'Not provided'}</span>
                                        </div>
                                    </div>

                                    {/* Email */}
                                    <div className='space-y-2'>
                                        <label className='block text-sm font-medium text-gray-700'>
                                            <FaEnvelope className='inline mr-2' />
                                            Email Address
                                        </label>
                                        <div className='p-3 bg-white border border-gray-200 rounded-md'>
                                            <span className='text-gray-900'>{userInfo?.email || 'Not provided'}</span>
                                        </div>
                                    </div>

                                    {/* Registration Method */}
                                    <div className='space-y-2'>
                                        <label className='block text-sm font-medium text-gray-700'>
                                            Registration Method
                                        </label>
                                        <div className='p-3 bg-white border border-gray-200 rounded-md'>
                                            <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full ${
                                                userInfo?.method === 'google'
                                                    ? 'bg-red-100 text-red-800'
                                                    : 'bg-blue-100 text-blue-800'
                                            }`}>
                                                {userInfo?.method === 'google' && <FaGoogle />}
                                                {userInfo?.method === 'google' ? 'Google Account' : 'Manual Registration'}
                                            </span>
                                        </div>
                                    </div>

                                    {/* Member Since */}
                                    <div className='space-y-2'>
                                        <label className='block text-sm font-medium text-gray-700'>
                                            <FaCalendar className='inline mr-2' />
                                            Member Since
                                        </label>
                                        <div className='p-3 bg-white border border-gray-200 rounded-md'>
                                            <span className='text-gray-900'>{formatDate(userInfo?.iat)}</span>
                                        </div>
                                    </div>
                                </div>

                                {/* Account Status */}
                                <div className='mt-6 p-4 bg-green-50 border border-green-200 rounded-md'>
                                    <h4 className='text-sm font-medium text-green-800 mb-2'>Account Status</h4>
                                    <div className='flex items-center gap-2'>
                                        <div className='w-2 h-2 bg-green-500 rounded-full'></div>
                                        <span className='text-sm text-green-700'>Active Account</span>
                                    </div>
                                </div>

                                {/* Note about profile updates */}
                                <div className='mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md'>
                                    <h4 className='text-sm font-medium text-blue-800 mb-2'>Profile Updates</h4>
                                    {userInfo?.method === 'google' ? (
                                        <p className='text-sm text-blue-700'>
                                            Your account is linked with Google. Profile information is managed through your Google account.
                                            You can change your password using the "Change Password" tab above.
                                        </p>
                                    ) : (
                                        <p className='text-sm text-blue-700'>
                                            To update your name or email address, please contact our support team.
                                            You can change your password using the "Change Password" tab above.
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'password' && (
                        <div className='max-w-md mx-auto'>
                            <div className='bg-gray-50 rounded-lg p-6'>
                                <h3 className='text-lg font-semibold mb-4 flex items-center gap-2 text-slate-600'>
                                    <FaLock />
                                    Change Password
                                </h3>
                                
                                <form onSubmit={handlePasswordChange} className='space-y-4'>
                                    <div>
                                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                                            Current Password
                                        </label>
                                        <input
                                            type="password"
                                            value={passwordData.old_password}
                                            onChange={(e) => setPasswordData(prev => ({ ...prev, old_password: e.target.value }))}
                                            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                                            placeholder='Enter current password'
                                            required
                                        />
                                    </div>
                                    
                                    <div>
                                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                                            New Password
                                        </label>
                                        <input
                                            type="password"
                                            value={passwordData.new_password}
                                            onChange={(e) => setPasswordData(prev => ({ ...prev, new_password: e.target.value }))}
                                            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                                            placeholder='Enter new password'
                                            required
                                        />
                                        <p className='text-xs text-gray-500 mt-1'>
                                            Password must be at least 6 characters long
                                        </p>
                                    </div>
                                    
                                    <div>
                                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                                            Confirm New Password
                                        </label>
                                        <input
                                            type="password"
                                            value={passwordData.confirm_password}
                                            onChange={(e) => setPasswordData(prev => ({ ...prev, confirm_password: e.target.value }))}
                                            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                                            placeholder='Confirm new password'
                                            required
                                        />
                                    </div>

                                    {/* Password strength indicator */}
                                    {passwordData.new_password && (
                                        <div className='space-y-2'>
                                            <label className='block text-sm font-medium text-gray-700'>
                                                Password Strength
                                            </label>
                                            <div className='flex space-x-1'>
                                                <div className={`h-2 w-1/4 rounded ${
                                                    passwordData.new_password.length >= 6 ? 'bg-green-500' : 'bg-gray-200'
                                                }`}></div>
                                                <div className={`h-2 w-1/4 rounded ${
                                                    passwordData.new_password.length >= 8 ? 'bg-green-500' : 'bg-gray-200'
                                                }`}></div>
                                                <div className={`h-2 w-1/4 rounded ${
                                                    /[A-Z]/.test(passwordData.new_password) ? 'bg-green-500' : 'bg-gray-200'
                                                }`}></div>
                                                <div className={`h-2 w-1/4 rounded ${
                                                    /[0-9]/.test(passwordData.new_password) ? 'bg-green-500' : 'bg-gray-200'
                                                }`}></div>
                                            </div>
                                            <p className='text-xs text-gray-500'>
                                                Include uppercase letters and numbers for a stronger password
                                            </p>
                                        </div>
                                    )}

                                    <button
                                        type="submit"
                                        disabled={loader}
                                        className='w-full py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center'
                                    >
                                        {loader ? (
                                            <PropagateLoader color='#fff' cssOverride={overrideStyle} size={8} />
                                        ) : (
                                            <>
                                                <FaLock className='mr-2' />
                                                Change Password
                                            </>
                                        )}
                                    </button>
                                </form>

                                {/* Security tips */}
                                <div className='mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md'>
                                    <h4 className='text-sm font-medium text-yellow-800 mb-2'>Security Tips</h4>
                                    <ul className='text-sm text-yellow-700 space-y-1'>
                                        <li>• Use a unique password for your account</li>
                                        <li>• Include uppercase, lowercase, numbers, and symbols</li>
                                        <li>• Avoid using personal information in passwords</li>
                                        <li>• Change your password regularly</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ProfileManagement;
