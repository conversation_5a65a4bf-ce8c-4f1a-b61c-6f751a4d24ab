import React from 'react';
import { <PERSON>a<PERSON><PERSON>ck, FaShippingFast, FaTruck, FaBoxOpen, FaHome } from 'react-icons/fa';

const ShippingProgress = ({ status, orderDate }) => {
  // Define shipping stages
  const stages = [
    {
      key: 'placed',
      label: 'Order Placed',
      icon: <FaCheck />,
      description: 'Your order has been placed successfully'
    },
    {
      key: 'pending',
      label: 'Processing',
      icon: <FaBoxOpen />,
      description: 'Your order is being prepared'
    },
    {
      key: 'warehouse',
      label: 'In Warehouse',
      icon: <FaShippingFast />,
      description: 'Your order is ready for shipping'
    },
    {
      key: 'shipped',
      label: 'Shipped',
      icon: <FaTruck />,
      description: 'Your order is on the way'
    },
    {
      key: 'delivered',
      label: 'Delivered',
      icon: <FaHome />,
      description: 'Your order has been delivered'
    }
  ];

  // Map status to stage index
  const getStageIndex = (currentStatus) => {
    const statusMap = {
      'placed': 0,
      'pending': 1,
      'warehouse': 2,
      'shipped': 3,
      'delivered': 4,
      'cancelled': -1
    };
    return statusMap[currentStatus] || 0;
  };

  const currentStageIndex = getStageIndex(status);
  const isCancelled = status === 'cancelled';

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-slate-700">Shipping Progress</h3>
        <span className="text-sm text-slate-500">
          Order Date: {new Date(orderDate).toLocaleDateString()}
        </span>
      </div>

      {isCancelled ? (
        <div className="text-center py-8">
          <div className="text-red-500 text-4xl mb-4">❌</div>
          <h4 className="text-xl font-semibold text-red-600 mb-2">Order Cancelled</h4>
          <p className="text-slate-600">This order has been cancelled</p>
        </div>
      ) : (
        <div className="relative">
          {/* Progress Line */}
          <div className="absolute top-6 left-6 right-6 h-0.5 bg-slate-200">
            <div 
              className="h-full bg-green-500 transition-all duration-500"
              style={{ 
                width: currentStageIndex >= 0 ? `${(currentStageIndex / (stages.length - 1)) * 100}%` : '0%' 
              }}
            />
          </div>

          {/* Stages */}
          <div className="relative flex justify-between">
            {stages.map((stage, index) => {
              const isCompleted = index <= currentStageIndex;
              const isCurrent = index === currentStageIndex;
              
              return (
                <div key={stage.key} className="flex flex-col items-center">
                  {/* Stage Icon */}
                  <div className={`
                    w-12 h-12 rounded-full flex items-center justify-center text-white text-lg
                    transition-all duration-300 relative z-10
                    ${isCompleted 
                      ? 'bg-green-500 shadow-lg' 
                      : isCurrent 
                        ? 'bg-blue-500 shadow-lg animate-pulse' 
                        : 'bg-slate-300'
                    }
                  `}>
                    {stage.icon}
                  </div>
                  
                  {/* Stage Label */}
                  <div className="mt-3 text-center max-w-[120px]">
                    <h4 className={`
                      text-sm font-semibold
                      ${isCompleted || isCurrent ? 'text-slate-700' : 'text-slate-400'}
                    `}>
                      {stage.label}
                    </h4>
                    <p className={`
                      text-xs mt-1
                      ${isCompleted || isCurrent ? 'text-slate-500' : 'text-slate-400'}
                    `}>
                      {stage.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Status Message */}
      <div className="mt-6 p-4 bg-slate-50 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-semibold text-slate-700">Current Status</h4>
            <p className="text-sm text-slate-600 mt-1">
              {isCancelled 
                ? 'Your order has been cancelled' 
                : stages[currentStageIndex]?.description || 'Processing your order'
              }
            </p>
          </div>
          <div className={`
            px-3 py-1 rounded-full text-xs font-semibold
            ${isCancelled 
              ? 'bg-red-100 text-red-800'
              : currentStageIndex === stages.length - 1
                ? 'bg-green-100 text-green-800'
                : 'bg-blue-100 text-blue-800'
            }
          `}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </div>
        </div>
      </div>

      {/* Estimated Delivery */}
      {!isCancelled && currentStageIndex < stages.length - 1 && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
          <div className="flex items-center">
            <FaTruck className="text-blue-500 mr-2" />
            <div>
              <h5 className="text-sm font-semibold text-blue-800">Estimated Delivery</h5>
              <p className="text-xs text-blue-600">
                {currentStageIndex < 2 
                  ? '3-5 business days' 
                  : currentStageIndex < 3 
                    ? '1-2 business days' 
                    : 'Today'
                }
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShippingProgress;
