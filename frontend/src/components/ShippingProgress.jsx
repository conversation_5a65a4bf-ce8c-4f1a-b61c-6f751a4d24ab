import React from 'react';
import { <PERSON>a<PERSON><PERSON>ck, FaShippingFast, FaTruck, FaBoxOpen, FaHome } from 'react-icons/fa';

const ShippingProgress = ({ status = 'placed', orderDate }) => {
  // Ensure status is defined and handle edge cases
  const currentStatus = status || 'placed';

  // Define shipping stages
  const stages = [
    {
      key: 'placed',
      label: 'Order Placed',
      icon: <FaCheck />,
      description: 'Your order has been placed successfully'
    },
    {
      key: 'pending',
      label: 'Processing',
      icon: <FaBoxOpen />,
      description: 'Your order is being prepared'
    },
    {
      key: 'warehouse',
      label: 'In Warehouse',
      icon: <FaShippingFast />,
      description: 'Your order is ready for shipping'
    },
    {
      key: 'shipped',
      label: 'Shipped',
      icon: <FaTruck />,
      description: 'Your order is on the way'
    },
    {
      key: 'delivered',
      label: 'Delivered',
      icon: <FaHome />,
      description: 'Your order has been delivered'
    }
  ];

  // Map status to stage index
  const getStageIndex = (currentStatus) => {
    const statusMap = {
      'placed': 0,
      'pending': 1,
      'warehouse': 2,
      'shipped': 3,
      'delivered': 4,
      'cancelled': -1
    };
    return statusMap[currentStatus] || 0;
  };

  const currentStageIndex = getStageIndex(currentStatus);
  const isCancelled = currentStatus === 'cancelled';

  return (
    <div className="bg-gradient-to-r from-blue-50 to-green-50 p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold text-slate-700 flex items-center">
          <span className="mr-2">🚚</span>
          Shipping Progress
        </h3>
        <span className="text-sm text-slate-500 bg-white px-3 py-1 rounded-full">
          Order Date: {orderDate ? new Date(orderDate).toLocaleDateString() : 'N/A'}
        </span>
      </div>

      {isCancelled ? (
        <div className="text-center py-12 bg-white rounded-lg">
          <div className="text-red-500 text-6xl mb-4">❌</div>
          <h4 className="text-2xl font-semibold text-red-600 mb-2">Order Cancelled</h4>
          <p className="text-slate-600 text-lg">This order has been cancelled</p>
          <div className="mt-4 p-3 bg-red-50 rounded-lg border border-red-200">
            <p className="text-sm text-red-700">If you have any questions about this cancellation, please contact our support team.</p>
          </div>
        </div>
      ) : (
        <div className="relative bg-white p-6 rounded-lg">
          {/* Progress Line */}
          <div className="absolute top-12 left-12 right-12 h-1 bg-slate-200 rounded-full">
            <div
              className="h-full bg-gradient-to-r from-green-400 to-green-600 transition-all duration-1000 ease-out rounded-full"
              style={{
                width: currentStageIndex >= 0 ? `${(currentStageIndex / (stages.length - 1)) * 100}%` : '0%'
              }}
            />
          </div>

          {/* Stages */}
          <div className="relative flex justify-between">
            {stages.map((stage, index) => {
              const isCompleted = index <= currentStageIndex;
              const isCurrent = index === currentStageIndex;
              
              return (
                <div key={stage.key} className="flex flex-col items-center">
                  {/* Stage Icon */}
                  <div className={`
                    w-16 h-16 rounded-full flex items-center justify-center text-white text-xl
                    transition-all duration-500 relative z-10 border-4
                    ${isCompleted
                      ? 'bg-gradient-to-br from-green-400 to-green-600 border-green-200 shadow-lg transform scale-110'
                      : isCurrent
                        ? 'bg-gradient-to-br from-blue-400 to-blue-600 border-blue-200 shadow-lg animate-pulse transform scale-105'
                        : 'bg-slate-300 border-slate-200'
                    }
                  `}>
                    {stage.icon}
                  </div>
                  
                  {/* Stage Label */}
                  <div className="mt-3 text-center max-w-[120px]">
                    <h4 className={`
                      text-sm font-semibold
                      ${isCompleted || isCurrent ? 'text-slate-700' : 'text-slate-400'}
                    `}>
                      {stage.label}
                    </h4>
                    <p className={`
                      text-xs mt-1
                      ${isCompleted || isCurrent ? 'text-slate-500' : 'text-slate-400'}
                    `}>
                      {stage.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Status Message */}
      <div className="mt-6 p-6 bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h4 className="text-lg font-semibold text-slate-700 mb-2">Current Status</h4>
            <p className="text-slate-600">
              {isCancelled
                ? 'Your order has been cancelled'
                : stages[currentStageIndex]?.description || 'Processing your order'
              }
            </p>
          </div>
          <div className={`
            px-4 py-2 rounded-full text-sm font-semibold shadow-sm
            ${isCancelled
              ? 'bg-red-100 text-red-800 border border-red-200'
              : currentStageIndex === stages.length - 1
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-blue-100 text-blue-800 border border-blue-200'
            }
          `}>
            {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1)}
          </div>
        </div>
      </div>

      {/* Estimated Delivery */}
      {!isCancelled && currentStageIndex < stages.length - 1 && (
        <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
          <div className="flex items-center">
            <div className="bg-blue-500 p-2 rounded-full mr-3">
              <FaTruck className="text-white text-lg" />
            </div>
            <div>
              <h5 className="text-lg font-semibold text-blue-800">Estimated Delivery</h5>
              <p className="text-blue-600 font-medium">
                {currentStageIndex < 2
                  ? '3-5 business days'
                  : currentStageIndex < 3
                    ? '1-2 business days'
                    : 'Today'
                }
              </p>
              <p className="text-xs text-blue-500 mt-1">
                We'll notify you when your order is out for delivery
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Delivery Confirmation */}
      {!isCancelled && currentStageIndex === stages.length - 1 && (
        <div className="mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
          <div className="flex items-center">
            <div className="bg-green-500 p-2 rounded-full mr-3">
              <FaHome className="text-white text-lg" />
            </div>
            <div>
              <h5 className="text-lg font-semibold text-green-800">Order Delivered! 🎉</h5>
              <p className="text-green-600 font-medium">
                Your order has been successfully delivered
              </p>
              <p className="text-xs text-green-500 mt-1">
                Thank you for shopping with us!
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShippingProgress;
