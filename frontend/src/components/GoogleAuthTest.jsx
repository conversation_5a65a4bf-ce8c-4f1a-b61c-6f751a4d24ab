import React, { useEffect, useState } from 'react';

const GoogleAuthTest = () => {
    const [status, setStatus] = useState({
        clientId: null,
        googleScript: false,
        initialized: false
    });

    useEffect(() => {
        // Check environment variable
        const clientId = process.env.REACT_APP_GOOGLE_CLIENT_ID;
        
        // Check if Google script is loaded
        const googleScript = !!window.google;
        
        setStatus({
            clientId: clientId || 'Not configured',
            googleScript,
            initialized: googleScript && !!clientId
        });

        // Try to initialize if everything is available
        if (googleScript && clientId) {
            try {
                window.google.accounts.id.initialize({
                    client_id: clientId,
                    callback: (response) => {
                        console.log('Google Auth Test - Credential received:', response);
                    }
                });
                setStatus(prev => ({ ...prev, initialized: true }));
            } catch (error) {
                console.error('Google Auth initialization error:', error);
            }
        }
    }, []);

    return (
        <div className="p-4 bg-gray-100 rounded-lg m-4">
            <h3 className="text-lg font-bold mb-4">🔍 Google OAuth Configuration Test</h3>
            
            <div className="space-y-2">
                <div className="flex items-center gap-2">
                    <span className={`w-3 h-3 rounded-full ${status.clientId && status.clientId !== 'Not configured' ? 'bg-green-500' : 'bg-red-500'}`}></span>
                    <span>Client ID: {status.clientId}</span>
                </div>
                
                <div className="flex items-center gap-2">
                    <span className={`w-3 h-3 rounded-full ${status.googleScript ? 'bg-green-500' : 'bg-red-500'}`}></span>
                    <span>Google Script Loaded: {status.googleScript ? 'Yes' : 'No'}</span>
                </div>
                
                <div className="flex items-center gap-2">
                    <span className={`w-3 h-3 rounded-full ${status.initialized ? 'bg-green-500' : 'bg-red-500'}`}></span>
                    <span>Google Auth Initialized: {status.initialized ? 'Yes' : 'No'}</span>
                </div>
            </div>

            {status.initialized && (
                <div className="mt-4 p-3 bg-green-100 border border-green-300 rounded">
                    ✅ Google OAuth is ready to use!
                </div>
            )}

            {!status.initialized && (
                <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded">
                    ❌ Google OAuth needs configuration. Check the setup guide.
                </div>
            )}
        </div>
    );
};

export default GoogleAuthTest;
