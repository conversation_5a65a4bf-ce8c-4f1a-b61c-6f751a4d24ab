import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { google_login } from '../store/reducers/authReducer';
import toast from 'react-hot-toast';
import { FadeLoader } from 'react-spinners';

const GoogleOAuthCallback = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();

    useEffect(() => {
        const handleOAuthCallback = async () => {
            const code = searchParams.get('code');
            const state = searchParams.get('state');
            const error = searchParams.get('error');

            // Handle OAuth errors
            if (error) {
                console.error('OAuth error:', error);
                toast.error('Google Sign-In was cancelled or failed.');
                navigate('/login');
                return;
            }

            // Handle successful OAuth response
            if (code) {
                try {
                    // Exchange code for user info
                    const response = await fetch(`https://oauth2.googleapis.com/token`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,
                            client_secret: '', // Not needed for public clients
                            code: code,
                            grant_type: 'authorization_code',
                            redirect_uri: window.location.origin,
                        }),
                    });

                    if (!response.ok) {
                        throw new Error('Failed to exchange code for token');
                    }

                    const tokenData = await response.json();
                    
                    // Get user info from Google
                    const userResponse = await fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${tokenData.access_token}`);
                    
                    if (!userResponse.ok) {
                        throw new Error('Failed to get user info');
                    }

                    const userData = await userResponse.json();

                    // Prepare data for our backend
                    const googleUserData = {
                        email: userData.email,
                        name: userData.name,
                        googleId: userData.id,
                        picture: userData.picture
                    };

                    // Dispatch login action
                    const result = await dispatch(google_login(googleUserData));
                    
                    if (google_login.fulfilled.match(result)) {
                        toast.success('Google Sign-In successful!');
                        navigate('/');
                    } else {
                        throw new Error('Login failed');
                    }

                } catch (error) {
                    console.error('OAuth callback error:', error);
                    toast.error('Google Sign-In failed. Please try again.');
                    navigate('/login');
                }
            } else {
                // No code parameter, redirect to login
                navigate('/login');
            }
        };

        handleOAuthCallback();
    }, [searchParams, dispatch, navigate]);

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center">
                <FadeLoader color="#059473" size={50} />
                <p className="mt-4 text-gray-600">Completing Google Sign-In...</p>
                <p className="text-sm text-gray-500 mt-2">Please wait while we log you in.</p>
            </div>
        </div>
    );
};

export default GoogleOAuthCallback;
