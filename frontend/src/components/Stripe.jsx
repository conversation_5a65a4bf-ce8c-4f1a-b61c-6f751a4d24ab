import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js'
import { Elements } from '@stripe/react-stripe-js'
import api from '../api/api';
import CheckoutForm from './CheckoutForm';

// Use environment variable for Stripe publishable key, fallback to test key for development
const stripePublishableKey = process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || 'pk_test_51PAnoIH8Ybj7y4HslHaP6gn540XnLicEW5HZIcyFhj98zUCHuZZMeQIX0p51STSXFc4SSbUDEOUWSyuoRg2llFpd00asOn8HKG';
const stripePromise = loadStripe(stripePublishableKey);

const Stripe = ({ price, orderId }) => {
  const [clientSecret, setClientSecret] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  const appearance = {
    theme: 'stripe'
  }
  const options = {
    appearance,
    clientSecret
  }

  const create_payment = async () => {
    if (!price || price <= 0) {
      setError('Invalid price amount')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await api.post('/order/create-payment', { price })

      if (response && response.data && response.data.clientSecret) {
        setClientSecret(response.data.clientSecret)
      } else {
        throw new Error('Invalid response from payment service')
      }
    } catch (error) {
      console.error('Payment creation error:', error)

      if (error.response && error.response.data) {
        setError(error.response.data.message || 'Payment creation failed')
      } else if (error.message) {
        setError(error.message)
      } else {
        setError('Unable to create payment. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className='mt-4'>
      {error && (
        <div className='mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded'>
          {error}
        </div>
      )}

      {
        clientSecret ? (
          <Elements options={options} stripe={stripePromise}>
            <CheckoutForm orderId={orderId} />
          </Elements>
        ) : (
          <button
            onClick={create_payment}
            disabled={loading}
            className={`px-10 py-[6px] rounded-sm hover:shadow-green-700/30 hover:shadow-lg text-white ${
              loading ? 'bg-gray-500 cursor-not-allowed' : 'bg-green-700'
            }`}
          >
            {loading ? 'Creating Payment...' : 'Start Payment'}
          </button>
        )
      }
    </div>
  );
};

export default Stripe;