import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../api/api";

export const place_order = createAsyncThunk(
    'order/place_order',
    async ({ price, products, shipping_fee, items, shippingInfo, userId, navigate }, { rejectWithValue, fulfillWithValue }) => {
        try {
            const { data } = await api.post('/home/<USER>/place-order', {
                price, products, shipping_fee, items, shippingInfo, userId
            })
            navigate('/payment', {
                state: {
                    price: price + shipping_fee,
                    items,
                    orderId: data.orderId
                }
            })
            return fulfillWithValue(data)
        } catch (error) {
            return rejectWithValue(error.response?.data || { error: 'Failed to place order', message: 'Network error' })
        }
    }
)
// End Method 

export const get_orders = createAsyncThunk(
    'order/get_orders',
    async ({ customerId, status }, { rejectWithValue, fulfillWithValue }) => {
        try {
            const { data } = await api.get(`/home/<USER>/get-orders/${customerId}/${status}`)
            // console.log(data)
            return fulfillWithValue(data)
        } catch (error) {
            return rejectWithValue(error.response?.data || { error: 'Failed to get orders', message: 'Network error' })
        }
    }
)
// End Method 

export const get_order_details = createAsyncThunk(
    'order/get_order_details',
    async (orderId, { rejectWithValue, fulfillWithValue }) => {
        try {
            const { data } = await api.get(`/home/<USER>/get-order-details/${orderId}`)
            // console.log(data)
            return fulfillWithValue(data)
        } catch (error) {
            return rejectWithValue(error.response?.data || { error: 'Failed to get order details', message: 'Network error' })
        }
    }
)// End Method

// Confirm order COD
export const confirm_order_cod = createAsyncThunk(
    'order/confirm_order_cod',
    async (orderId, { rejectWithValue, fulfillWithValue }) => {
        try {
            const { data } = await api.get(`/home/<USER>/confirm-cod/${orderId}`);
            return fulfillWithValue(data);
        } catch (error) {
            return rejectWithValue(error.response?.data || { error: 'Failed to confirm order', message: 'Network error' });
        }
    }
);// End Method


export const orderReducer = createSlice({
    name: 'order',
    initialState: {
        myOrders: [],
        errorMessage: '',
        successMessage: '',
        myOrder: {},
    },
    reducers: {
        messageClear: (state, _) => {
            state.errorMessage = ""
            state.successMessage = ""
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(place_order.rejected, (state, { payload }) => {
                state.errorMessage = payload?.error || payload?.message || 'Failed to place order';
            })
            .addCase(place_order.fulfilled, (state, { payload }) => {
                state.successMessage = payload?.message || 'Order placed successfully';
            })

            .addCase(get_orders.rejected, (state, { payload }) => {
                state.errorMessage = payload?.error || payload?.message || 'Failed to get orders';
            })
            .addCase(get_orders.fulfilled, (state, { payload }) => {
                state.myOrders = payload.orders;
            })

            .addCase(get_order_details.rejected, (state, { payload }) => {
                state.errorMessage = payload?.error || payload?.message || 'Failed to get order details';
            })
            .addCase(get_order_details.fulfilled, (state, { payload }) => {
                state.myOrder = payload.order;
            })
            // CONFIRM ORDER COD
            .addCase(confirm_order_cod.pending, (state) => {
                state.successMessage = '';
                state.errorMessage = '';
            })
            .addCase(confirm_order_cod.fulfilled, (state, { payload }) => {
                state.successMessage = payload.message;
            })
            .addCase(confirm_order_cod.rejected, (state, { payload }) => {
                state.errorMessage = payload.message || "Something went wrong!";
            });

    }
})
export const { messageClear } = orderReducer.actions
export default orderReducer.reducer