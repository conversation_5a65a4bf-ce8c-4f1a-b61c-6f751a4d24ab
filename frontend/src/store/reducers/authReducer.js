import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import api from "../../api/api";
import { jwtDecode } from "jwt-decode";

export const customer_register = createAsyncThunk(
    'auth/customer_register',
    async (info, { rejectWithValue, fulfillWithValue }) => {
        try {
            const { data } = await api.post('/customer/customer-register', info)
            // save token localStorage
            localStorage.setItem('customerToken', data.token)
            // console.log(data)
            return fulfillWithValue(data)
        } catch (error) {
            return rejectWithValue(error.response.data)
        }
    }
)
// End Method 

export const customer_login = createAsyncThunk(
    'auth/customer_login',
    async (info, { rejectWithValue, fulfillWithValue }) => {
        try {
            const { data } = await api.post('/customer/customer-login', info)
            // get token
            localStorage.setItem('customerToken', data.token)
            // console.log(data)
            return fulfillWithValue(data)
        } catch (error) {
            return rejectWithValue(error.response.data)
        }
    }
)
// End Method

export const google_login = createAsyncThunk(
    'auth/google_login',
    async (info, { rejectWithValue, fulfillWithValue }) => {
        try {
            const { data } = await api.post('/customer/google-login', info)
            // save token localStorage
            localStorage.setItem('customerToken', data.token)
            return fulfillWithValue(data)
        } catch (error) {
            return rejectWithValue(error.response.data)
        }
    }
)
// End Method

/// Chanage Password method
export const customer_change_password = createAsyncThunk(
    'auth/customer_change_password',
    async ({ email, info }, { rejectWithValue, fulfillWithValue }) => {
        try {
            const { data } = await api.post('/customer-change-password', { email, ...info }, { withCredentials: true })
            // console.log(data)            
            return fulfillWithValue(data.message)
        } catch (error) {
            // console.log(error.response.data)
            return rejectWithValue(error.response.data.message)
        }
    }
)// end method 

const decodeToken = (token) => {
    if (token) {
        const userInfo = jwtDecode(token)
        return userInfo
    } else {
        return ''
    }
}// End Method 

export const authReducer = createSlice({
    name: 'auth',
    initialState: {
        loader: false,
        userInfo: decodeToken(localStorage.getItem('customerToken')),
        errorMessage: '',
        successMessage: '',
    },
    reducers: {
        messageClear: (state, _) => {
            state.errorMessage = ""
            state.successMessage = ""
        },
        user_reset: (state, _) => {
            state.userInfo = ""
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(customer_register.pending, (state, { payload }) => {
                state.loader = true;
            })
            .addCase(customer_register.rejected, (state, { payload }) => {
                state.errorMessage = payload.error;
                state.loader = false;
            })
            .addCase(customer_register.fulfilled, (state, { payload }) => {
                const userInfo = decodeToken(payload.token)
                state.successMessage = payload.message;
                state.loader = false;
                state.userInfo = userInfo
            })

            .addCase(customer_login.pending, (state, { payload }) => {
                state.loader = true;
            })
            .addCase(customer_login.rejected, (state, { payload }) => {
                state.errorMessage = payload.error;
                state.loader = false;
            })
            .addCase(customer_login.fulfilled, (state, { payload }) => {
                const userInfo = decodeToken(payload.token)
                state.successMessage = payload.message;
                state.loader = false;
                state.userInfo = userInfo
            })

            .addCase(google_login.pending, (state, { payload }) => {
                state.loader = true;
            })
            .addCase(google_login.rejected, (state, { payload }) => {
                state.errorMessage = payload.error;
                state.loader = false;
            })
            .addCase(google_login.fulfilled, (state, { payload }) => {
                const userInfo = decodeToken(payload.token)
                state.successMessage = payload.message;
                state.loader = false;
                state.userInfo = userInfo
            })
            /// change Password
            .addCase(customer_change_password.pending, (state) => {
                state.loader = true;
                state.errorMessage = null;
            })
            .addCase(customer_change_password.rejected, (state, action) => {
                state.loader = false;
                state.errorMessage = action.payload;
            })
            .addCase(customer_change_password.fulfilled, (state, action) => {
                state.loader = false;
                state.successMessage = action.payload
            })

    }
})
export const { messageClear, user_reset } = authReducer.actions
export default authReducer.reducer