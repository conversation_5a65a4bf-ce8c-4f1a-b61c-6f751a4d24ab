import axios from "axios";

const local = 'http://localhost:9000';
const production = 'https://backend-multi-vendors-ecommerce.onrender.com';

// Use environment variable if available, otherwise detect based on NODE_ENV
const getApiUrl = () => {
    // If API URL is explicitly set in environment variables, use it
    if (process.env.REACT_APP_API_URL) {
        return process.env.REACT_APP_API_URL;
    }

    // Otherwise, use mode-based detection
    const mode = process.env.NODE_ENV === 'production' ? 'pro' : 'dev';
    const baseUrl = mode === 'pro' ? production : local;
    return `${baseUrl}/api`;
};

const api = axios.create({
    baseURL: getApiUrl(),
    withCredentials: true,
    timeout: 10000, // 10 second timeout
});

// Add request interceptor for debugging
api.interceptors.request.use(
    (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
        return config;
    },
    (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
api.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        console.error('API Response Error:', error);

        // Handle different types of errors
        if (error.code === 'ECONNABORTED') {
            console.error('Request timeout');
            // Return a structured error for timeout
            return Promise.reject({
                response: {
                    data: {
                        error: 'Request timeout. Please try again.',
                        message: 'Request timeout'
                    }
                }
            });
        } else if (!error.response) {
            console.error('Network error - server may be down');
            // Return a structured error for network issues
            return Promise.reject({
                response: {
                    data: {
                        error: 'Network error. Please check your connection.',
                        message: 'Network error'
                    }
                }
            });
        }

        // Ensure error.response.data exists
        if (error.response && !error.response.data) {
            error.response.data = {
                error: 'An unexpected error occurred',
                message: 'Unknown error'
            };
        }

        return Promise.reject(error);
    }
);

export default api;
