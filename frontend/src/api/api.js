import axios from "axios";

const local = 'http://localhost:9000';
const production = 'https://backend-multi-vendors-ecommerce.onrender.com';

// Use environment variable if available, otherwise detect based on NODE_ENV
const getApiUrl = () => {
    // If API URL is explicitly set in environment variables, use it
    if (process.env.REACT_APP_API_URL) {
        return process.env.REACT_APP_API_URL;
    }

    // Otherwise, use mode-based detection
    const mode = process.env.NODE_ENV === 'production' ? 'pro' : 'dev';
    const baseUrl = mode === 'pro' ? production : local;
    return `${baseUrl}/api`;
};

const api = axios.create({
    baseURL: getApiUrl(),
    withCredentials: true,
    timeout: 10000, // 10 second timeout
});

// Add request interceptor for debugging
api.interceptors.request.use(
    (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
        return config;
    },
    (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
api.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        console.error('API Response Error:', error);

        if (error.code === 'ECONNABORTED') {
            console.error('Request timeout');
        } else if (!error.response) {
            console.error('Network error - server may be down');
        }

        return Promise.reject(error);
    }
);

export default api;
