import io from 'socket.io-client';

// Environment-aware socket URL
const getSocketUrl = () => {
    if (process.env.REACT_APP_SOCKET_URL) {
        return process.env.REACT_APP_SOCKET_URL;
    }
    return process.env.NODE_ENV === 'production' 
        ? 'https://backend-multi-vendors-ecommerce.onrender.com' 
        : 'http://localhost:9000';
};

// Singleton socket instance
let socket = null;

// Socket connection options
const socketOptions = {
    transports: ['websocket', 'polling'],
    timeout: 20000,
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionAttempts: 5,
    maxReconnectionAttempts: 5,
};

// Initialize socket connection
export const initializeSocket = () => {
    if (!socket) {
        socket = io(getSocketUrl(), socketOptions);
        
        socket.on('connect', () => {
            console.log('Socket connected:', socket.id);
        });

        socket.on('disconnect', (reason) => {
            console.log('Socket disconnected:', reason);
        });

        socket.on('connect_error', (error) => {
            console.error('Socket connection error:', error);
        });

        socket.on('reconnect', (attemptNumber) => {
            console.log('Socket reconnected after', attemptNumber, 'attempts');
        });

        socket.on('reconnect_error', (error) => {
            console.error('Socket reconnection error:', error);
        });
    }
    return socket;
};

// Get existing socket instance
export const getSocket = () => {
    if (!socket) {
        return initializeSocket();
    }
    return socket;
};

// Disconnect socket
export const disconnectSocket = () => {
    if (socket) {
        socket.disconnect();
        socket = null;
    }
};

// Check if socket is connected
export const isSocketConnected = () => {
    return socket && socket.connected;
};

// Emit event with error handling
export const emitSocketEvent = (event, data) => {
    if (socket && socket.connected) {
        socket.emit(event, data);
        return true;
    } else {
        console.warn('Socket not connected, cannot emit event:', event);
        return false;
    }
};

// Listen to socket events with cleanup
export const onSocketEvent = (event, callback) => {
    if (socket) {
        socket.on(event, callback);
        return () => socket.off(event, callback);
    }
    return () => {};
};

const socketUtils = {
    initializeSocket,
    getSocket,
    disconnectSocket,
    isSocketConnected,
    emitSocketEvent,
    onSocketEvent
};

export default socketUtils;
