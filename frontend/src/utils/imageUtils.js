/**
 * Helper function to get the correct path for images
 * @param {string} imagePath - The relative path to the image
 * @returns {string} The full path to the image
 */
export const getImagePath = (imagePath) => {
  return `${process.env.PUBLIC_URL}${imagePath}`;
};

/**
 * Banner image paths
 */
export const bannerImages = {
  shop: getImagePath('/images/banner/shop.png'),
  // Add more as needed
};