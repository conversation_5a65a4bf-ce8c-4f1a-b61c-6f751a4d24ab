{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emailjs/browser": "4.4.1", "@reduxjs/toolkit": "2.3.0", "@stripe/react-stripe-js": "3.6.0", "@stripe/stripe-js": "7.2.0", "axios": "1.7.9", "cra-template": "1.2.0", "jwt-decode": "4.0.0", "leaflet": "1.9.4", "react": "18.3.1", "react-dom": "18.3.1", "react-hot-toast": "2.5.1", "react-icons": "5.4.0", "react-leaflet": "4.2.1", "react-multi-carousel": "2.8.5", "react-range": "1.10.0", "react-rating": "2.0.5", "react-redux": "9.1.2", "react-router-dom": "6.28.0", "react-scripts": "5.0.1", "react-spinners": "0.15.0", "socket.io-client": "4.8.1", "stripe": "17.5.0", "swiper": "11.2.4", "web-vitals": "4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "check-images": "node scripts/check-images.js", "prestart": "npm run check-images"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.17", "@babel/plugin-proposal-private-property-in-object": "^7.21.11"}, "overrides": {"react": "18.3.1", "react-dom": "18.3.1"}, "resolutions": {"react": "18.3.1", "react-dom": "18.3.1"}}