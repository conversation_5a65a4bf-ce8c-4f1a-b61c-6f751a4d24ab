#!/bin/bash

# 🔄 Quick Update Script for Existing Deployment
# Updates your already deployed ecommerce application

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Vercel CLI is installed
check_vercel() {
    if ! command -v vercel &> /dev/null; then
        print_warning "Vercel CLI not found. Installing..."
        npm install -g vercel
        print_success "Vercel CLI installed!"
    else
        print_success "Vercel CLI found!"
    fi
}

# Update Frontend
update_frontend() {
    print_status "Updating Frontend..."
    cd frontend
    
    # Install dependencies (in case of new packages)
    print_status "Installing dependencies..."
    npm install
    
    # Build the project
    print_status "Building frontend..."
    npm run build
    
    if [ $? -eq 0 ]; then
        print_success "Frontend build successful!"
        
        # Deploy to Vercel
        print_status "Deploying to Vercel..."
        vercel --prod --yes
        
        if [ $? -eq 0 ]; then
            print_success "✅ Frontend updated successfully!"
            print_status "URL: https://frontend-multi-vendors-ercommerce.vercel.app"
        else
            print_error "Frontend deployment failed!"
            return 1
        fi
    else
        print_error "Frontend build failed!"
        return 1
    fi
    
    cd ..
}

# Update Dashboard
update_dashboard() {
    print_status "Updating Dashboard..."
    cd dashboard
    
    # Install dependencies
    print_status "Installing dependencies..."
    npm install
    
    # Build the project
    print_status "Building dashboard..."
    npm run build
    
    if [ $? -eq 0 ]; then
        print_success "Dashboard build successful!"
        
        # Deploy to Vercel
        print_status "Deploying to Vercel..."
        vercel --prod --yes
        
        if [ $? -eq 0 ]; then
            print_success "✅ Dashboard updated successfully!"
            print_status "URL: https://dashboard-multi-vendors-ecommerce.vercel.app"
        else
            print_error "Dashboard deployment failed!"
            return 1
        fi
    else
        print_error "Dashboard build failed!"
        return 1
    fi
    
    cd ..
}

# Update Backend (via Git push)
update_backend() {
    print_status "Updating Backend..."
    print_warning "Backend is deployed on Render and updates via Git push."
    print_status "Make sure to commit and push your changes to trigger auto-deployment."
    
    echo ""
    print_status "To update backend:"
    echo "1. git add ."
    echo "2. git commit -m 'Update backend with fixes'"
    echo "3. git push origin main"
    echo ""
    print_status "Or manually redeploy at: https://dashboard.render.com"
    print_success "✅ Backend update instructions provided!"
}

# Test deployments
test_deployments() {
    print_status "Testing deployments..."
    
    echo ""
    print_status "🔗 Your Application URLs:"
    echo "Backend:   https://backend-multi-vendors-ecommerce.onrender.com"
    echo "Frontend:  https://frontend-multi-vendors-ercommerce.vercel.app"
    echo "Dashboard: https://dashboard-multi-vendors-ecommerce.vercel.app"
    echo ""
    
    print_warning "📋 Please test the following:"
    echo "✓ User registration/login"
    echo "✓ Product browsing and search"
    echo "✓ Shopping cart functionality"
    echo "✓ Stripe payment processing"
    echo "✓ Chat functionality"
    echo "✓ Admin/seller dashboard"
    echo ""
}

# Main function
main() {
    echo "🔄 Updating Existing Deployment"
    echo "==============================="
    echo ""
    print_status "Current Deployment:"
    print_status "Backend: Render (Auto-deploy via Git)"
    print_status "Frontend: Vercel"
    print_status "Dashboard: Vercel"
    echo ""
    
    check_vercel
    
    echo ""
    echo "What would you like to update?"
    echo "1) Frontend only"
    echo "2) Dashboard only"
    echo "3) Both Frontend & Dashboard"
    echo "4) Backend instructions"
    echo "5) All (Frontend + Dashboard + Backend instructions)"
    
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            update_frontend
            ;;
        2)
            update_dashboard
            ;;
        3)
            update_frontend
            update_dashboard
            ;;
        4)
            update_backend
            ;;
        5)
            update_frontend
            update_dashboard
            update_backend
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac
    
    echo ""
    test_deployments
    
    print_success "🎉 Update process completed!"
    echo ""
    print_warning "💡 Pro Tips:"
    echo "• Use 'vercel --prod --force' to force redeploy if needed"
    echo "• Check 'vercel logs' if there are any issues"
    echo "• Monitor your applications for a few minutes after update"
    echo ""
}

# Run main function
main
