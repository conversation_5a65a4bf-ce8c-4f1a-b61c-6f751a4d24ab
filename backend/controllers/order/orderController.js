const AuthOrderModel = require('../../models/authOrder')
const CustomerOrder = require('../../models/customerOrder')

const MyShopWallet = require('../../models/myShopWallet')
const SellerWallet = require('../../models/sellerWallet')

const CartModel = require('../../models/cartModel')
const moment = require("moment")
const { responseReturn } = require('../../utils/response')
const { mongo: { ObjectId } } = require('mongoose')
const stripe = require('stripe')('sk_test_51PAnoIH8Ybj7y4HsPGuTzvafuryAfaDbWWf3wSiupfmoFgNxD5sqPdTRzXZSOzFQ84U5V5gXFeCzuzG4Aq3MAjy400pHXITjy9')


class orderController {

    paymentCheck = async (id) => {
        try {
            const order = await CustomerOrder.findById(id)
            if (order.payment_status === 'unpaid') {
                await CustomerOrder.findByIdAndUpdate(id, {
                    delivery_status: 'cancelled'
                })
                await AuthOrderModel.updateMany({
                    orderId: id
                }, {
                    delivery_status: 'cancelled'
                })
            }
            return true
        } catch (error) {
            console.log(error)
        }
    } // end method 

    place_order = async (req, res) => {
        // Destructure the required data from the request body
        const { price, products, shipping_fee, shippingInfo, userId } = req.body;

        // Initialize arrays to store order data for authors and card IDs
        let authorOrderData = [];
        let cartId = [];

        // Get the current date and format it
        const tempDate = moment(Date.now()).format('LLL');

        // Initialize an array to store customer's ordered products
        let customerOrderProduct = [];

        // Loop through the products array to process each product
        for (let i = 0; i < products.length; i++) {
            const pro = products[i].products;

            // Loop through each product in the nested array
            for (let j = 0; j < pro.length; j++) {
                const tempCusPro = pro[j].productInfo;

                // Add the quantity to the product info
                tempCusPro.quantity = pro[j].quantity;

                // Push the product info to the customer's order array
                customerOrderProduct.push(tempCusPro);

                // If the product has an ID, add it to the cartId array
                if (pro[j]._id) {
                    cartId.push(pro[j]._id);
                }
            }
        }

        try {
            // Create a new customer order in the database
            const order = await CustomerOrder.create({
                customerId: userId, // ID of the customer placing the order
                shippingInfo, // Shipping information
                products: customerOrderProduct, // List of ordered products
                price: price + shipping_fee, // Total price including shipping fee
                payment_status: 'unpaid', // Default payment status
                delivery_status: 'pending', // Default delivery status
                date: tempDate // Order date
            });

            // Loop through the products array again to prepare data for author orders
            for (let i = 0; i < products.length; i++) {
                const pro = products[i].products;
                const pri = products[i].price;
                const sellerId = products[i].sellerId;
                let storePor = [];

                // Loop through each product in the nested array
                for (let j = 0; j < pro.length; j++) {
                    const tempPro = pro[j].productInfo;

                    // Add the quantity to the product info
                    tempPro.quantity = pro[j].quantity;

                    // Push the product info to the store's product array
                    storePor.push(tempPro);
                }

                // Prepare data for the author's order
                authorOrderData.push({
                    orderId: order.id, // ID of the customer order
                    sellerId, // ID of the seller
                    products: storePor, // List of products for this seller
                    price: pri, // Total price for this seller's products
                    payment_status: 'unpaid', // Default payment status
                    shippingInfo: 'Easy Main Warehouse', // Default shipping info
                    delivery_status: 'pending', // Default delivery status
                    date: tempDate // Order date
                });
            }

            // Insert all author orders into the database
            await AuthOrderModel.insertMany(authorOrderData);

            // Delete the products from the cart after the order is placed
            for (let k = 0; k < cartId.length; k++) {
                await CartModel.findByIdAndDelete(cartId[k]);
            }

            // Schedule a payment check after 15 seconds
            setTimeout(() => {
                this.paymentCheck(order.id);
            }, 15000);

            // Return a success response with the order ID
            responseReturn(res, 200, { message: "Order Placed Success", orderId: order.id });

        } catch (error) {
            // Log any errors that occur during the process
            console.log(error.message);
        }
    }; //end method

    get_customer_dashboard_data = async (req, res) => {
        const { userId } = req.params
        try {
            const recentOrders = await CustomerOrder.find({
                customerId: new ObjectId(userId)
            })
                .sort({ createdAt: -1 }) // Sort by newest first
                .limit(5); // Limit to 3 orders as you mentioned

            const pendingOrder = await CustomerOrder.find({
                customerId: new ObjectId(userId),
                delivery_status: 'pending'
            }).countDocuments()

            const totalOrder = await CustomerOrder.find({
                customerId: new ObjectId(userId)
            }).countDocuments()

            const cancelledOrder = await CustomerOrder.find({
                customerId: new ObjectId(userId),
                delivery_status: 'cancelled'
            }).countDocuments()

            responseReturn(res, 200, {
                recentOrders,
                pendingOrder,
                totalOrder,
                cancelledOrder
            })

        } catch (error) {
            console.log(error.message)
        }

    }// End Method 

    get_orders = async (req, res) => {
        const { customerId, status } = req.params
        try {
            let orders = []
            if (status !== 'all') {
                orders = await CustomerOrder.find({
                    customerId: new ObjectId(customerId),
                    delivery_status: status
                })
            } else {
                orders = await CustomerOrder.find({
                    customerId: new ObjectId(customerId)
                })
            }
            responseReturn(res, 200, {
                orders
            })

        } catch (error) {
            console.log(error.message)
        }

    } // End Method

    get_order_details = async (req, res) => {
        const { orderId } = req.params
        try {
            const order = await CustomerOrder.findById(orderId)
            responseReturn(res, 200, {
                order
            })

        } catch (error) {
            console.log(error.message)
        }
    }// End Method 

    get_admin_orders = async (req, res) => {
        let { page, searchValue, parPage } = req.query
        page = parseInt(page)
        parPage = parseInt(parPage)

        const skipPage = parPage * (page - 1)
        try {
            if (searchValue) {
            } else {
                const orders = await CustomerOrder.aggregate([
                    {
                        $lookup: {
                            from: 'authororders',
                            localField: "_id",
                            foreignField: 'orderId',
                            as: 'suborder'
                        }
                    }
                ]).skip(skipPage).limit(parPage).sort({ createdAt: -1 })

                const totalOrder = await CustomerOrder.aggregate([
                    {
                        $lookup: {
                            from: 'authororders',
                            localField: "_id",
                            foreignField: 'orderId',
                            as: 'suborder'
                        }
                    }
                ])
                responseReturn(res, 200, { orders, totalOrder: totalOrder.length })
            }
        } catch (error) {
            console.log(error.message)
        }
    }// End Method

    get_admin_order = async (req, res) => {
        const { orderId } = req.params
        try {
            const order = await CustomerOrder.aggregate([
                {
                    $match: { _id: new ObjectId(orderId) }
                },
                {
                    $lookup: {
                        from: 'authororders',
                        localField: "_id",
                        foreignField: 'orderId',
                        as: 'suborder'
                    }
                }
            ])
            responseReturn(res, 200, { order: order[0] })
        } catch (error) {
            console.log('get admin order details' + error.message)
        }
    }// End Method

    admin_order_status_update = async (req, res) => {
        const { orderId } = req.params
        const { status } = req.body
        try {
            await CustomerOrder.findByIdAndUpdate(orderId, {
                delivery_status: status
            })
            responseReturn(res, 200, { message: 'order Status change success' })
        } catch (error) {
            console.log('get admin status error' + error.message)
            responseReturn(res, 500, { message: 'internal server error' })
        }
    }// End Method 

    get_seller_orders = async (req, res) => {
        const { sellerId } = req.params
        let { page, searchValue, parPage } = req.query
        page = parseInt(page)
        parPage = parseInt(parPage)
        const skipPage = parPage * (page - 1)
        try {
            if (searchValue) {
            } else {
                const orders = await AuthOrderModel.find({
                    sellerId,
                }).skip(skipPage).limit(parPage).sort({ createdAt: -1 })
                const totalOrder = await AuthOrderModel.find({
                    sellerId
                }).countDocuments()
                responseReturn(res, 200, { orders, totalOrder })
            }
        } catch (error) {
            console.log('get seller Order error' + error.message)
            responseReturn(res, 500, { message: 'internal server error' })
        }
    }// End Method 

    get_seller_order = async (req, res) => {
        const { orderId } = req.params
        try {
            const order = await AuthOrderModel.findById(orderId)
            responseReturn(res, 200, { order })
        } catch (error) {
            console.log('get seller details error' + error.message)
        }
    }// End Method 

    seller_order_status_update = async (req, res) => {
        const { orderId } = req.params
        const { status } = req.body
        try {
            await AuthOrderModel.findByIdAndUpdate(orderId, {
                delivery_status: status
            })
            responseReturn(res, 200, { message: 'order status updated successfully' })
        } catch (error) {
            console.log('get seller Order error' + error.message)
            responseReturn(res, 500, { message: 'internal server error' })
        }
    }// End Method 

    create_payment = async (req, res) => {
        const { price } = req.body
        try {
            const payment = await stripe.paymentIntents.create({
                amount: price * 100,
                currency: 'usd',
                automatic_payment_methods: {
                    enabled: true
                }
            })
            responseReturn(res, 200, { clientSecret: payment.client_secret })
        } catch (error) {
            console.log(error.message)
        }
    }// End Method 

    order_confirm = async (req, res) => {
        const { orderId } = req.params
        try {
            await CustomerOrder.findByIdAndUpdate(orderId, { payment_status: 'paid', delivery_status: 'on-going' })
            await AuthOrderModel.updateMany({ orderId: new ObjectId(orderId) }, {
                payment_status: 'paid', delivery_status: 'on-going'
            })
            const cuOrder = await CustomerOrder.findById(orderId)

            const auOrder = await AuthOrderModel.find({
                orderId: new ObjectId(orderId)
            })

            const time = moment(Date.now()).format('l')
            const splitTime = time.split('/')

            await MyShopWallet.create({
                amount: cuOrder.price,
                month: splitTime[0],
                year: splitTime[2]
            })

            for (let i = 0; i < auOrder.length; i++) {
                await SellerWallet.create({
                    sellerId: auOrder[i].sellerId.toString(),
                    amount: auOrder[i].price,
                    month: splitTime[0],
                    year: splitTime[2]
                })
            }
            responseReturn(res, 200, { message: 'success' })

        } catch (error) {
            console.log(error.message)
        }

    }// End Method 

    order_confirm_cod = async (req, res) => {
        const { orderId } = req.params;
        try {
            // Update trạng thái thanh toán và giao hàng
            await CustomerOrder.findByIdAndUpdate(orderId, { payment_status: 'paid-cod', delivery_status: 'pending-cod' });
            await AuthOrderModel.updateMany({ orderId: new ObjectId(orderId) }, {
                payment_status: 'paid-cod',
                delivery_status: 'pending-cod'
            });

            // ❌ Không cập nhật Wallet ở đây vì tiền chưa thu về
            // ❌ Không tạo MyShopWallet
            // ❌ Không tạo SellerWallet

            responseReturn(res, 200, { message: 'COD order confirmed successfully' });

        } catch (error) {
            console.log(error.message);
            responseReturn(res, 500, { message: 'Internal server error' });
        }
    }; // End Method

}

module.exports = new orderController()