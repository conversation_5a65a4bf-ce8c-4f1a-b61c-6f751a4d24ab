const SellerModel = require("../../models/sellerModel");
const CustomerModel = require("../../models/customerModel");
const SellerCustomerModel = require("../../models/chat/sellerCustomerModel");
const SellerCustomerMessage = require("../../models/chat/sellerCustomerMessage");
const AdminSellerMessage = require('../../models/chat/adminSellerMessage')
const { responseReturn } = require("../../utils/response");

class ChatController {
    add_customer_friend = async (req, res) => {
        const { sellerId, userId } = req.body;

        try {
            if (sellerId !== "") {
                const seller = await SellerModel.findById(sellerId);
                const user = await CustomerModel.findById(userId);
                const checkSeller = await SellerCustomerModel.findOne({
                    $and: [
                        {
                            myId: {
                                $eq: userId,
                            },
                        },
                        {
                            myFriends: {
                                $elemMatch: {
                                    fdId: sellerId,
                                },
                            },
                        },
                    ],
                });
                if (!checkSeller) {
                    await SellerCustomerModel.updateOne(
                        {
                            myId: userId,
                        },
                        {
                            $push: {
                                myFriends: {
                                    fdId: sellerId,
                                    name: seller.shopInfo?.shopName,
                                    image: seller.image,
                                },
                            },
                        }
                    );
                }

                const checkCustomer = await SellerCustomerModel.findOne({
                    $and: [
                        {
                            myId: {
                                $eq: sellerId,
                            },
                        },
                        {
                            myFriends: {
                                $elemMatch: {
                                    fdId: userId,
                                },
                            },
                        },
                    ],
                });
                if (!checkCustomer) {
                    await SellerCustomerModel.updateOne(
                        {
                            myId: sellerId,
                        },
                        {
                            $push: {
                                myFriends: {
                                    fdId: userId,
                                    name: user.name,
                                    image: "",
                                },
                            },
                        }
                    );
                }
                const messages = await SellerCustomerMessage.find({
                    $or: [
                        {
                            $and: [
                                {
                                    receverId: { $eq: sellerId },
                                },
                                {
                                    senderId: {
                                        $eq: userId,
                                    },
                                },
                            ],
                        },
                        {
                            $and: [
                                {
                                    receverId: { $eq: userId },
                                },
                                {
                                    senderId: {
                                        $eq: sellerId,
                                    },
                                },
                            ],
                        },
                    ],
                });
                const MyFriends = await SellerCustomerModel.findOne({
                    myId: userId,
                });
                const currentFd = MyFriends.myFriends.find((s) => s.fdId === sellerId);
                responseReturn(res, 200, {
                    MyFriends: MyFriends.myFriends,
                    currentFd,
                    messages,
                });
            } else {
                const MyFriends = await SellerCustomerModel.findOne({
                    myId: userId,
                });
                responseReturn(res, 200, {
                    MyFriends: MyFriends.myFriends,
                });
            }
        } catch (error) {
            console.log(error);
        }
    }; // end method

    customer_message_add = async (req, res) => {
        const { userId, text, sellerId, name } = req.body

        try {
            const message = await SellerCustomerMessage.create({
                senderId: userId,
                senderName: name,
                receverId: sellerId,
                message: text
            })

            const data = await SellerCustomerModel.findOne({ myId: userId })
            let myFriends = data.myFriends
            let index = myFriends.findIndex(f => f.fdId === sellerId)
            while (index > 0) {
                let temp = myFriends[index]
                myFriends[index] = myFriends[index - 1]
                myFriends[index - 1] = temp
                index--
            }
            await SellerCustomerModel.updateOne(
                {
                    myId: userId
                },
                {
                    myFriends
                }
            )
            const data1 = await SellerCustomerModel.findOne({ myId: sellerId })
            let myFriends1 = data1.myFriends
            let index1 = myFriends1.findIndex(f => f.fdId === userId)
            while (index1 > 0) {
                let temp1 = myFriends1[index1]
                myFriends1[index1] = myFriends[index1 - 1]
                myFriends1[index1 - 1] = temp1
                index1--
            }
            await SellerCustomerModel.updateOne(
                {
                    myId: sellerId
                },
                {
                    myFriends1
                }
            )

            responseReturn(res, 201, { message })

        } catch (error) {
            console.log(error)
        }
    }// End Method

    get_customers = async (req, res) => {
        const { sellerId } = req.params
        try {
            const data = await SellerCustomerModel.findOne({ myId: sellerId })
            responseReturn(res, 200, {
                customers: data.myFriends
            })
        } catch (error) {
            console.log(error)
        }
    }// End Method

    get_customers_seller_message = async (req, res) => {
        const { customerId } = req.params
        const { id } = req

        try {
            const messages = await SellerCustomerMessage.find({
                $or: [
                    {
                        $and: [{
                            receverId: { $eq: customerId }
                        }, {
                            senderId: {
                                $eq: id
                            }
                        }]
                    },
                    {
                        $and: [{
                            receverId: { $eq: id }
                        }, {
                            senderId: {
                                $eq: customerId
                            }
                        }]
                    }
                ]
            })

            const currentCustomer = await CustomerModel.findById(customerId)
            responseReturn(res, 200, {
                messages,
                currentCustomer
            })

        } catch (error) {
            console.log(error)
        }

    }// End Method

    seller_message_add = async (req, res) => {
        const { senderId, receverId, text, name } = req.body
        try {
            const message = await SellerCustomerMessage.create({
                senderId: senderId,
                senderName: name,
                receverId: receverId,
                message: text
            })

            const data = await SellerCustomerModel.findOne({ myId: senderId })
            let myFriends = data.myFriends
            let index = myFriends.findIndex(f => f.fdId === receverId)
            while (index > 0) {
                let temp = myFriends[index]
                myFriends[index] = myFriends[index - 1]
                myFriends[index - 1] = temp
                index--
            }
            await SellerCustomerModel.updateOne(
                {
                    myId: senderId
                },
                {
                    myFriends
                }

            )

            const data1 = await SellerCustomerModel.findOne({ myId: receverId })
            let myFriends1 = data1.myFriends
            let index1 = myFriends1.findIndex(f => f.fdId === senderId)
            while (index1 > 0) {
                let temp1 = myFriends1[index1]
                myFriends1[index1] = myFriends[index1 - 1]
                myFriends1[index1 - 1] = temp1
                index1--
            }
            await SellerCustomerModel.updateOne(
                {
                    myId: receverId
                },
                {
                    myFriends1
                }
            )

            responseReturn(res, 201, { message })

        } catch (error) {
            console.log(error)
        }
    }// End Method 

    get_sellers = async (req, res) => {
        try {
            const sellers = await SellerModel.find({})
            responseReturn(res, 200, {
                sellers
            })
        } catch (error) {
            console.log(error)
        }
    }// End Method

    seller_admin_message_insert = async (req, res) => {
        const { senderId, receverId, message, senderName } = req.body

        try {
            const messageData = await AdminSellerMessage.create({
                senderId,
                receverId,
                message,
                senderName
            })
            responseReturn(res, 200, { message: messageData })
        } catch (error) {
            console.log(error)
        }
    } // End Method 

    get_admin_messages = async (req, res) => {
        const { receverId } = req.params
        const id = ""

        try {
            const messages = await AdminSellerMessage.find({
                $or: [
                    {
                        $and: [{
                            receverId: { $eq: receverId }
                        }, {
                            senderId: {
                                $eq: id
                            }
                        }]
                    },
                    {
                        $and: [{
                            receverId: { $eq: id }
                        }, {
                            senderId: {
                                $eq: receverId
                            }
                        }]
                    }
                ]
            })

            let currentSeller = {}
            if (receverId) {
                currentSeller = await SellerModel.findById(receverId)
            }
            responseReturn(res, 200, {
                messages,
                currentSeller
            })

        } catch (error) {
            console.log(error)
        }
    }// End Method

    get_seller_messages = async (req, res) => {
        const receverId = ""
        const { id } = req

        try {
            const messages = await AdminSellerMessage.find({
                $or: [
                    {
                        $and: [{
                            receverId: { $eq: receverId }
                        }, {
                            senderId: {
                                $eq: id
                            }
                        }]
                    },
                    {
                        $and: [{
                            receverId: { $eq: id }
                        }, {
                            senderId: {
                                $eq: receverId
                            }
                        }]
                    }
                ]
            })

            responseReturn(res, 200, {
                messages
            })

        } catch (error) {
            console.log(error)
        }
    }// End Method 

}

module.exports = new ChatController();
