const SellerModel = require('../../models/sellerModel')
const StripeModel = require('../../models/stripeModel')

const SellerWallet = require('../../models/sellerWallet')
const WithdrawRequest = require('../../models/withdrawRequest')

const { v4: uuidv4 } = require('uuid')
const { responseReturn } = require('../../utils/response')
const { mongo: { ObjectId } } = require('mongoose')
const stripe = require('stripe')('sk_test_51PAnoIH8Ybj7y4HsPGuTzvafuryAfaDbWWf3wSiupfmoFgNxD5sqPdTRzXZSOzFQ84U5V5gXFeCzuzG4Aq3MAjy400pHXITjy9')


class paymentController {

    create_stripe_connect_account = async (req, res) => {
        const { id } = req
        const uid = uuidv4()

        try {
            const stripeInfo = await StripeModel.findOne({ sellerId: id })

            if (stripeInfo) {
                await StripeModel.deleteOne({ sellerId: id })
                const account = await stripe.accounts.create({ type: 'express' })

                const accountLink = await stripe.accountLinks.create({
                    account: account.id,
                    refresh_url: 'https://dashboard-multi-vendors-ecommerce.vercel.app/refresh',
                    return_url: `https://dashboard-multi-vendors-ecommerce.vercel.app/success?activeCode=${uid}`,
                    type: 'account_onboarding'
                })
                await StripeModel.create({
                    sellerId: id,
                    stripeId: account.id,
                    code: uid
                })
                responseReturn(res, 201, { url: accountLink.url })

            } else {
                const account = await stripe.accounts.create({ type: 'express' })

                const accountLink = await stripe.accountLinks.create({
                    account: account.id,
                    refresh_url: 'https://dashboard-multi-vendors-ecommerce.vercel.app/refresh',
                    return_url: `https://dashboard-multi-vendors-ecommerce.vercel.app/success?activeCode=${uid}`,
                    type: 'account_onboarding'
                })
                await StripeModel.create({
                    sellerId: id,
                    stripeId: account.id,
                    code: uid
                })
                responseReturn(res, 201, { url: accountLink.url })
            }
        } catch (error) {
            console.log('strpe connect account errror' + error.message)
        }
    }
    // End Method 

    active_stripe_connect_account = async (req, res) => {
        const { activeCode } = req.params
        const { id } = req

        try {
            const userStripeInfo = await StripeModel.findOne({ code: activeCode })
            if (userStripeInfo) {
                await SellerModel.findByIdAndUpdate(id, {
                    payment: 'active'
                })
                responseReturn(res, 200, { message: 'payment Active' })
            } else {
                responseReturn(res, 404, { message: 'payment Active Fails' })
            }

        } catch (error) {
            responseReturn(res, 500, { message: 'Internal Server Error' })
        }

    }// End Method 

    sumAmount = (data) => {
        let sum = 0;
        for (let i = 0; i < data.length; i++) {
            sum = sum + data[i].amount;
        }
        return sum
    }

    get_seller_payment_details = async (req, res) => {
        const { sellerId } = req.params

        try {
            const payments = await SellerWallet.find({ sellerId })
            const pendingWithdraws = await WithdrawRequest.find({
                $and: [
                    {
                        sellerId: {
                            $eq: sellerId
                        }
                    },
                    {
                        status: {
                            $eq: 'pending'
                        }
                    }
                ]
            })

            const successWithdraws = await WithdrawRequest.find({
                $and: [
                    {
                        sellerId: {
                            $eq: sellerId
                        }
                    },
                    {
                        status: {
                            $eq: 'success'
                        }
                    }
                ]
            })

            const pendingAmount = this.sumAmount(pendingWithdraws)
            const withdrawAmount = this.sumAmount(successWithdraws)
            const totalAmount = this.sumAmount(payments)

            let availableAmount = 0;

            if (totalAmount > 0) {
                availableAmount = totalAmount - (pendingAmount + withdrawAmount)
            }

            responseReturn(res, 200, {
                totalAmount,
                pendingAmount,
                withdrawAmount,
                availableAmount,
                pendingWithdraws,
                successWithdraws
            })

        } catch (error) {
            console.log(error.message)
        }

    }// End Method 

    withdrawal_request = async (req, res) => {
        const { amount, sellerId } = req.body

        try {
            const withdrawal = await WithdrawRequest.create({
                sellerId,
                amount: parseInt(amount)
            })
            responseReturn(res, 200, { withdrawal, message: 'Withdrawal Request Send' })
        } catch (error) {
            responseReturn(res, 500, { message: 'Internal Server Error' })
        }
    }// End Method 

    get_payment_request = async (req, res) => {
        try {
            const withdrawalRequest = await WithdrawRequest.find({ status: 'pending' })
            responseReturn(res, 200, { withdrawalRequest })
        } catch (error) {
            responseReturn(res, 500, { message: 'Internal Server Error' })
        }
    }// End Method 

    payment_request_confirm = async (req, res) => {
        const { paymentId } = req.body
        try {
            const payment = await WithdrawRequest.findById(paymentId)
            const { stripeId } = await StripeModel.findOne({
                sellerId: new ObjectId(payment.sellerId)
            })

            await stripe.transfers.create({
                amount: payment.amount * 100,
                currency: 'usd',
                destination: stripeId
            })

            await WithdrawRequest.findByIdAndUpdate(paymentId, { status: 'success' })
            responseReturn(res, 200, { payment, message: 'Request Confirm Success' })

        } catch (error) {
            responseReturn(res, 500, { message: 'Internal Server Error' })
        }
    }// End Method 

}


module.exports = new paymentController()