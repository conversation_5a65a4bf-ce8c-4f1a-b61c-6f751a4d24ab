const SellerModel = require('../../models/sellerModel')
const StripeModel = require('../../models/stripeModel')

const SellerWallet = require('../../models/sellerWallet')
const WithdrawRequest = require('../../models/withdrawRequest')

const { v4: uuidv4 } = require('uuid')
const { responseReturn } = require('../../utils/response')
const { mongo: { ObjectId } } = require('mongoose')
const stripe = require('stripe')('sk_test_51PAnoIH8Ybj7y4HsPGuTzvafuryAfaDbWWf3wSiupfmoFgNxD5sqPdTRzXZSOzFQ84U5V5gXFeCzuzG4Aq3MAjy400pHXITjy9')


class paymentController {

    create_stripe_connect_account = async (req, res) => {
        const { id } = req
        const uid = uuidv4()

        try {
            const stripeInfo = await StripeModel.findOne({ sellerId: id })

            if (stripeInfo) {
                await StripeModel.deleteOne({ sellerId: id })
                const account = await stripe.accounts.create({ type: 'express' })

                const accountLink = await stripe.accountLinks.create({
                    account: account.id,
                    refresh_url: 'https://dashboard-multi-vendors-ecommerce.vercel.app/refresh',
                    return_url: `https://dashboard-multi-vendors-ecommerce.vercel.app/success?activeCode=${uid}`,
                    type: 'account_onboarding'
                })
                await StripeModel.create({
                    sellerId: id,
                    stripeId: account.id,
                    code: uid
                })
                responseReturn(res, 201, { url: accountLink.url })

            } else {
                const account = await stripe.accounts.create({ type: 'express' })

                const accountLink = await stripe.accountLinks.create({
                    account: account.id,
                    refresh_url: 'https://dashboard-multi-vendors-ecommerce.vercel.app/refresh',
                    return_url: `https://dashboard-multi-vendors-ecommerce.vercel.app/success?activeCode=${uid}`,
                    type: 'account_onboarding'
                })
                await StripeModel.create({
                    sellerId: id,
                    stripeId: account.id,
                    code: uid
                })
                responseReturn(res, 201, { url: accountLink.url })
            }
        } catch (error) {
            console.log('strpe connect account errror' + error.message)
        }
    }
    // End Method 

    active_stripe_connect_account = async (req, res) => {
        const { activeCode } = req.params
        const { id } = req

        try {
            const userStripeInfo = await StripeModel.findOne({ code: activeCode })
            if (userStripeInfo) {
                await SellerModel.findByIdAndUpdate(id, {
                    payment: 'active'
                })
                responseReturn(res, 200, { message: 'payment Active' })
            } else {
                responseReturn(res, 404, { message: 'payment Active Fails' })
            }

        } catch (error) {
            responseReturn(res, 500, { message: 'Internal Server Error' })
        }

    }// End Method 

    sumAmount = (data) => {
        let sum = 0;
        for (let i = 0; i < data.length; i++) {
            sum = sum + data[i].amount;
        }
        return sum
    }

    get_seller_payment_details = async (req, res) => {
        const { sellerId } = req.params

        try {
            const payments = await SellerWallet.find({ sellerId })
            const pendingWithdraws = await WithdrawRequest.find({
                $and: [
                    {
                        sellerId: {
                            $eq: sellerId
                        }
                    },
                    {
                        status: {
                            $eq: 'pending'
                        }
                    }
                ]
            })

            const successWithdraws = await WithdrawRequest.find({
                $and: [
                    {
                        sellerId: {
                            $eq: sellerId
                        }
                    },
                    {
                        status: {
                            $eq: 'success'
                        }
                    }
                ]
            })

            const pendingAmount = this.sumAmount(pendingWithdraws)
            const withdrawAmount = this.sumAmount(successWithdraws)
            const totalAmount = this.sumAmount(payments)

            let availableAmount = 0;

            if (totalAmount > 0) {
                availableAmount = totalAmount - (pendingAmount + withdrawAmount)
            }

            responseReturn(res, 200, {
                totalAmount,
                pendingAmount,
                withdrawAmount,
                availableAmount,
                pendingWithdraws,
                successWithdraws
            })

        } catch (error) {
            console.log(error.message)
        }

    }// End Method 

    withdrawal_request = async (req, res) => {
        const { amount, sellerId } = req.body

        try {
            // Validate input
            if (!amount || !sellerId) {
                return responseReturn(res, 400, { message: 'Amount and seller ID are required' })
            }

            const withdrawalAmount = parseInt(amount)
            if (isNaN(withdrawalAmount) || withdrawalAmount <= 0) {
                return responseReturn(res, 400, { message: 'Invalid withdrawal amount' })
            }

            // Check if seller exists and has Stripe account
            const stripeAccount = await StripeModel.findOne({ sellerId: new ObjectId(sellerId) })
            if (!stripeAccount || !stripeAccount.stripeId) {
                return responseReturn(res, 400, { message: 'Seller must connect Stripe account before requesting withdrawal' })
            }

            const withdrawal = await WithdrawRequest.create({
                sellerId,
                amount: withdrawalAmount,
                status: 'pending',
                createdAt: new Date()
            })
            responseReturn(res, 200, { withdrawal, message: 'Withdrawal Request Send' })
        } catch (error) {
            console.error('Withdrawal request error:', error)
            responseReturn(res, 500, {
                message: 'Internal Server Error',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            })
        }
    }// End Method

    get_payment_request = async (req, res) => {
        try {
            const withdrawalRequest = await WithdrawRequest.find({ status: 'pending' })
                .populate('sellerId', 'name email shopInfo')
                .sort({ createdAt: -1 })

            responseReturn(res, 200, { withdrawalRequest })
        } catch (error) {
            console.error('Get payment requests error:', error)
            responseReturn(res, 500, {
                message: 'Internal Server Error',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            })
        }
    }// End Method

    payment_request_confirm = async (req, res) => {
        const { paymentId } = req.body
        try {
            // Validate paymentId
            if (!paymentId) {
                return responseReturn(res, 400, { message: 'Payment ID is required' })
            }

            // Find the withdrawal request
            const payment = await WithdrawRequest.findById(paymentId)
            if (!payment) {
                return responseReturn(res, 404, { message: 'Payment request not found' })
            }

            // Check if already processed
            if (payment.status === 'success') {
                return responseReturn(res, 400, { message: 'Payment request already confirmed' })
            }

            // Find seller's Stripe account
            const stripeAccount = await StripeModel.findOne({
                sellerId: new ObjectId(payment.sellerId)
            })

            if (!stripeAccount || !stripeAccount.stripeId) {
                return responseReturn(res, 400, { message: 'Seller Stripe account not found or not connected' })
            }

            // Create Stripe transfer
            const transfer = await stripe.transfers.create({
                amount: Math.round(payment.amount * 100), // Convert to cents and ensure integer
                currency: 'usd',
                destination: stripeAccount.stripeId,
                description: `Withdrawal for seller ${payment.sellerId}`
            })

            // Update payment status
            const updatedPayment = await WithdrawRequest.findByIdAndUpdate(
                paymentId,
                {
                    status: 'success',
                    transferId: transfer.id,
                    processedAt: new Date()
                },
                { new: true }
            )

            responseReturn(res, 200, {
                payment: updatedPayment,
                message: 'Request Confirm Success',
                transferId: transfer.id
            })

        } catch (error) {
            console.error('Payment confirmation error:', error)

            // Handle specific Stripe errors
            if (error.type === 'StripeCardError') {
                return responseReturn(res, 400, { message: 'Stripe payment error: ' + error.message })
            } else if (error.type === 'StripeInvalidRequestError') {
                return responseReturn(res, 400, { message: 'Invalid payment request: ' + error.message })
            } else if (error.type === 'StripeAPIError') {
                return responseReturn(res, 500, { message: 'Stripe API error: ' + error.message })
            }

            responseReturn(res, 500, {
                message: 'Internal Server Error',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            })
        }
    }// End Method

}


module.exports = new paymentController()