const formidable = require("formidable")
const { responseReturn } = require("../../utils/response")
const cloudinary = require('cloudinary').v2
const ProductModel = require('../../models/productModel')
const SellerModel = require('../../models/sellerModel')

class productController {
    add_product = async (req, res) => {
        const { id } = req;
        const form = formidable({ multiples: true });

        form.parse(req, async (err, fields, files) => {
            if (err) {
                return responseReturn(res, 400, { error: 'Form parsing error', details: err.message });
            }

            try {
                let { name = '', category = '', description = '', stock = 0, price = 0, discount = 0, brand = '' } = fields;
                let { images } = files;
                name = name.trim();
                const slug = name.split(' ').join('-');

                if (!name || !category || !price || !stock) {
                    return responseReturn(res, 400, { error: 'Missing required fields' });
                }

                if (!images) {
                    return responseReturn(res, 400, { error: 'At least one image is required' });
                }

                cloudinary.config({
                    cloud_name: process.env.cloud_name,
                    api_key: process.env.api_key,
                    api_secret: process.env.api_secret,
                    secure: true
                });

                const shopInfo = await SellerModel.findById(id);
                if (!shopInfo) {
                    return responseReturn(res, 404, { error: 'Shop not found' });
                }

                const allImageUrl = [];
                if (!Array.isArray(images)) images = [images];

                for (const img of images) {
                    const result = await cloudinary.uploader.upload(img.filepath, { folder: 'products' });
                    allImageUrl.push(result.url);
                }

                await ProductModel.create({
                    sellerId: id,
                    name,
                    slug,
                    shopName: shopInfo.shopInfo.shopName,
                    category: category.trim(),
                    description: description.trim(),
                    stock: parseInt(stock),
                    price: parseInt(price),
                    discount: parseInt(discount),
                    images: allImageUrl,
                    brand: brand.trim()
                });

                return responseReturn(res, 201, { message: 'Product Added Successfully!' });

            } catch (error) {
                return responseReturn(res, 500, { error: error.message });
            }
        });
    };// end method

    products_get = async (req, res) => {
        const { page, searchValue, parPage } = req.query
        const { id } = req;
        const skipPage = parseInt(parPage) * (parseInt(page) - 1)

        try {
            if (searchValue) {
                const products = await ProductModel.find({
                    $text: { $search: searchValue },
                    sellerId: id
                }).skip(skipPage).limit(parPage).sort({ createdAt: -1 })
                const totalProduct = await ProductModel.find({
                    $text: { $search: searchValue },
                    sellerId: id
                }).countDocuments()
                responseReturn(res, 200, { products, totalProduct })
            } else {
                const products = await ProductModel.find({ sellerId: id }).skip(skipPage).limit(parPage).sort({ createdAt: -1 })
                const totalProduct = await ProductModel.find({ sellerId: id }).countDocuments()
                responseReturn(res, 200, { products, totalProduct })
            }

        } catch (error) {
            console.log(error.message)
        }

    } // End Method

    product_get = async (req, res) => {
        const { productId } = req.params;
        try {
            const product = await ProductModel.findById(productId)
            responseReturn(res, 200, { product })
        } catch (error) {
            console.log(error.message)
        }
    } // End Method 

    product_update = async (req, res) => {
        let { name, category, description, stock, price, discount, brand, productId } = req.body;
        name = name.trim()
        const slug = name.split(' ').join('-')

        try {
            await ProductModel.findByIdAndUpdate(productId, {
                name, category, description, stock, price, discount, brand, productId, slug
            })
            const product = await ProductModel.findById(productId)
            responseReturn(res, 200, { product, message: 'Product Updated Successfully !' })
        } catch (error) {
            responseReturn(res, 500, { error: error.message })
        }

    } // End Method 

    product_image_update = async (req, res) => {
        const form = formidable({ multiples: true })

        form.parse(req, async (err, field, files) => {
            const { oldImage, productId } = field;
            const { newImage } = files

            if (err) {
                responseReturn(res, 400, { error: err.message })
            } else {
                try {

                    cloudinary.config({
                        cloud_name: process.env.cloud_name,
                        api_key: process.env.api_key,
                        api_secret: process.env.api_secret,
                        secure: true
                    })

                    const result = await cloudinary.uploader.upload(newImage.filepath, { folder: 'products' })

                    if (result) {
                        let { images } = await ProductModel.findById(productId)
                        const index = images.findIndex(img => img === oldImage)
                        images[index] = result.url;
                        await ProductModel.findByIdAndUpdate(productId, { images })

                        const product = await ProductModel.findById(productId)
                        responseReturn(res, 200, { product, message: 'Product Image Updated Successfully !' })

                    } else {
                        responseReturn(res, 404, { error: 'Image Upload Failed !' })
                    }
                } catch (error) {
                    responseReturn(res, 404, { error: error.message })
                }
            }
        })
    } // End Method

    delete_product = async (req, res) => {
        try {
            const productId = req.params.id;
            const deleteProduct = await ProductModel.findByIdAndDelete(productId);

            if (!deleteProduct) {
                // console.log(`Product with id ${productId} not found`);
                return res.status(404).json({ message: 'Product not found !' });
            }
            res.status(200).json({ message: 'Product deleted successfully !' });

        } catch (error) {
            console.log(`Error delete Product with id ${productId}:`, error);
            res.status(500).json({ message: 'Internal Server Error !' });
        }

    } // end method

}

module.exports = new productController()