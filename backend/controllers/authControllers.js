const AdminModel = require('../models/adminModel.js')
const SellerModel = require('../models/sellerModel')
const SellerCustomerModel = require('../models/chat/sellerCustomerModel')
const { responseReturn } = require('../utils/response')
const bcrypt = require('bcrypt')
const { createToken } = require('../utils/tokenCreate')
const cloudinary = require('cloudinary').v2
const formidable = require("formidable")

class authControllers {
    admin_login = async (req, res) => {
        const { email, password } = req.body
        try {
            const admin = await AdminModel.findOne({ email }).select('+password')
            // console.log(admin)
            if (admin) {
                const match = await bcrypt.compare(password, admin.password)
                // console.log(match)
                if (match) {
                    const token = await createToken({
                        id: admin.id,
                        role: admin.role
                    })
                    res.cookie('accessToken', token, {
                        httpOnly: true,
                        secure: true,
                        sameSite: 'none',
                        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 ngày
                        // expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
                    })
                    responseReturn(res, 200, { token, message: "Login Success !" })
                } else {
                    responseReturn(res, 404, { error: "Password Wrong !" })
                }
            } else {
                responseReturn(res, 404, { error: "Email not Found !" })
            }
        } catch (error) {
            responseReturn(res, 500, { error: error.message })
        }
    } // end of admin_login

    seller_login = async (req, res) => {
        const { email, password } = req.body
        try {
            const seller = await SellerModel.findOne({ email }).select('+password')
            // console.log(admin)
            if (seller) {
                const match = await bcrypt.compare(password, seller.password)
                // console.log(match)
                if (match) {
                    const token = await createToken({
                        id: seller.id,
                        role: seller.role
                    })
                    res.cookie('accessToken', token, {
                        httpOnly: true,
                        secure: true,
                        sameSite: 'none',
                        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 ngày
                        // expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
                    })
                    responseReturn(res, 200, { token, message: "Login Success !" })
                } else {
                    responseReturn(res, 404, { error: "Password Wrong !" })
                }
            } else {
                responseReturn(res, 404, { error: "Email not Found !" })
            }
        } catch (error) {
            responseReturn(res, 500, { error: error.message })
        }

    } // End Method 

    seller_register = async (req, res) => {
        const { email, name, password } = req.body
        try {
            const getUser = await SellerModel.findOne({ email })
            if (getUser) {
                responseReturn(res, 404, { error: 'Email Already Exit !' })
            } else {
                const seller = await SellerModel.create({
                    name,
                    email,
                    password: await bcrypt.hash(password, 10), // hash password
                    method: 'menualy',
                    shopInfo: {}
                })
                // create ID seller
                await SellerCustomerModel.create({
                    myId: seller.id
                })
                // create token
                const token = await createToken({ id: seller.id, role: seller.role })
                res.cookie('accessToken', token, {
                    expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
                })

                responseReturn(res, 201, { token, message: 'Register Success !' })
            }
        } catch (error) {
            responseReturn(res, 500, { error: 'Internal Server Error !' })
        }
    } // End Method 

    getUser = async (req, res) => {
        const { id, role } = req;
        try {
            if (role === 'admin') {
                const user = await AdminModel.findById(id)
                responseReturn(res, 200, { userInfo: user })
            } else {
                //console.log('Seller infor')
                const seller = await SellerModel.findById(id)
                responseReturn(res, 200, { userInfo: seller })
            }

        } catch (error) {
            responseReturn(res, 500, { error: 'Internal Server Error' })
        }

    } // end of getUser

    profile_image_upload = async (req, res) => {
        const { id } = req
        const form = formidable({ multiples: true })
        form.parse(req, async (err, _, files) => {
            cloudinary.config({
                cloud_name: process.env.cloud_name,
                api_key: process.env.api_key,
                api_secret: process.env.api_secret,
                secure: true
            })
            const { image } = files

            try {
                const result = await cloudinary.uploader.upload(image.filepath, { folder: 'profile' })
                if (result) {
                    await SellerModel.findByIdAndUpdate(id, {
                        image: result.url
                    })
                    const userInfo = await SellerModel.findById(id)
                    responseReturn(res, 201, { message: 'Profile Image Upload Successfully !', userInfo })
                } else {
                    responseReturn(res, 404, { error: 'Image Upload Failed' })
                }

            } catch (error) {
                responseReturn(res, 500, { error: error.message })
            }
        })
    } // End Method

    profile_info_add = async (req, res) => {
        const { division, district, shopName, sub_district } = req.body;
        const { id } = req;

        try {
            await SellerModel.findByIdAndUpdate(id, {
                shopInfo: {
                    shopName,
                    division,
                    district,
                    sub_district
                }
            })
            const userInfo = await SellerModel.findById(id)
            responseReturn(res, 201, { message: 'Profile info Add Successfully', userInfo })

        } catch (error) {
            responseReturn(res, 500, { error: error.message })
        }
    } // End Method 

    /// Change Password seller
    change_password = async (req, res) => {
        const { email, old_password, new_password } = req.body;
        // console.log(email,old_password,new_password)
        try {
            const user = await SellerModel.findOne({ email }).select('+password');
            if (!user) return res.status(404).json({ message: 'Seller not found' });

            const isMatch = await bcrypt.compare(old_password, user.password);
            if (!isMatch) return res.status(400).json({ message: 'Incorrect old password' });

            user.password = await bcrypt.hash(new_password, 10);
            await user.save();
            res.json({ message: 'Password changed successfully' });

        } catch (error) {
            res.status(500).json({ message: 'Server Error' });
        }
    }// End Method 

    logout = async (req, res) => {
        try {
            res.cookie('accessToken', null, {
                expires: new Date(Date.now()),
                httpOnly: true
            })
            responseReturn(res, 200, { message: 'logout Success' })
        } catch (error) {
            responseReturn(res, 500, { error: error.message })
        }
    }// End Method 

}

module.exports = new authControllers();