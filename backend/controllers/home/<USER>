const CartModel = require('../../models/cartModel')
const { responseReturn } = require('../../utils/response')
const { mongo: { ObjectId } } = require('mongoose')
const WishlistModel = require('../../models/wishlistModel')

class cartController {
    add_to_cart = async (req, res) => {
        const { userId, productId, quantity } = req.body
        try {
            const product = await CartModel.findOne({
                $and: [{
                    productId: {
                        $eq: productId
                    }
                },
                {
                    userId: {
                        $eq: userId
                    }
                }
                ]
            })

            if (product) {
                responseReturn(res, 404, { error: "Product Already Added To Cart !" })
            } else {
                const product = await CartModel.create({
                    userId,
                    productId,
                    quantity
                })
                responseReturn(res, 201, { message: "Added To Cart Successfully !", product })
            }

        } catch (error) {
            console.log(error.message)
        }
    }// end method

    get_cart_products = async (req, res) => {
        const co = 5; // Discount coefficient
        const { userId } = req.params; // Extract userId from request parameters

        try {
            // Fetch cart products for the given user and join with product details
            const cart_products = await CartModel.aggregate([
                {
                    $match: {
                        userId: new ObjectId(userId) // Match cart items by userId
                    }
                },
                {
                    $lookup: {
                        from: 'products', // Join with products collection
                        localField: 'productId', // Field from cart collection
                        foreignField: "_id", // Field from products collection
                        as: 'products' // Output array field name
                    }
                }
            ]);

            let buy_product_item = 0; // Total quantity of products in stock
            let calculatePrice = 0; // Total price of products in stock
            let cart_product_count = 0; // Total quantity of products (in stock + out of stock)

            // Filter out-of-stock products
            const outOfStockProduct = cart_products.filter(p => p.products[0].stock < p.quantity);
            // Calculate total quantity of out-of-stock products
            for (let i = 0; i < outOfStockProduct.length; i++) {
                cart_product_count += outOfStockProduct[i].quantity;
            }

            // Filter in-stock products
            const stockProduct = cart_products.filter(p => p.products[0].stock >= p.quantity);
            // Calculate total quantity and price of in-stock products
            for (let i = 0; i < stockProduct.length; i++) {
                const { quantity } = stockProduct[i];
                cart_product_count += quantity; // Update total product count

                buy_product_item += quantity; // Update total in-stock product count
                const { price, discount } = stockProduct[i].products[0];
                // Calculate price considering discount
                if (discount !== 0) {
                    calculatePrice += quantity * (price - Math.floor((price * discount) / 100));
                } else {
                    calculatePrice += quantity * price;
                }
            }

            // Group products by seller and calculate seller-specific prices
            let p = []; // Array to store seller-specific product details
            let unique = [...new Set(stockProduct.map(p => p.products[0].sellerId.toString()))]; // Unique seller IDs
            for (let i = 0; i < unique.length; i++) {
                let price = 0; // Total price for the seller
                for (let j = 0; j < stockProduct.length; j++) {
                    const tempProduct = stockProduct[j].products[0];
                    if (unique[i] === tempProduct.sellerId.toString()) {
                        let pri = 0; // Price after discount and coefficient
                        if (tempProduct.discount !== 0) {
                            pri = tempProduct.price - Math.floor((tempProduct.price * tempProduct.discount) / 100);
                        } else {
                            pri = tempProduct.price;
                        }
                        pri = pri - Math.floor((pri * co) / 100); // Apply additional discount coefficient
                        price += pri * stockProduct[j].quantity; // Update total price for the seller

                        // Group products by seller
                        p[i] = {
                            sellerId: unique[i],
                            shopName: tempProduct.shopName,
                            price,
                            products: p[i] ? [
                                ...p[i].products,
                                {
                                    _id: stockProduct[j]._id,
                                    quantity: stockProduct[j].quantity,
                                    productInfo: tempProduct
                                }
                            ] : [{
                                _id: stockProduct[j]._id,
                                quantity: stockProduct[j].quantity,
                                productInfo: tempProduct
                            }]
                        };
                    }
                }
            }

            // Send response with cart details
            responseReturn(res, 200, {
                cart_products: p, // Seller-specific product details
                price: calculatePrice, // Total price of in-stock products
                cart_product_count, // Total quantity of products (in stock + out of stock)
                shipping_fee: 20 * p.length, // Shipping fee based on number of sellers
                outOfStockProduct, // Out-of-stock products
                buy_product_item // Total quantity of in-stock products
            });

        } catch (error) {
            console.log(error.message); // Log error message
            responseReturn(res, 500, { error: "Internal Server Error" }); // Send error response
        }
    };// end method

    delete_cart_products = async (req, res) => {
        const { cart_id } = req.params
        try {
            await CartModel.findByIdAndDelete(cart_id)
            responseReturn(res, 200, { message: "Product Remove Successfully !" })

        } catch (error) {
            console.log(error.message)
        }
    }// End Method

    quantity_inc = async (req, res) => {
        const { cart_id } = req.params
        try {
            const product = await CartModel.findById(cart_id)
            const { quantity } = product
            await CartModel.findByIdAndUpdate(cart_id, { quantity: quantity + 1 })
            responseReturn(res, 200, { message: "Qty Updated !" })

        } catch (error) {
            console.log(error.message)
        }

    }// End Method 

    quantity_dec = async (req, res) => {
        const { cart_id } = req.params
        try {
            const product = await CartModel.findById(cart_id)
            const { quantity } = product
            await CartModel.findByIdAndUpdate(cart_id, { quantity: quantity - 1 })
            responseReturn(res, 200, { message: "Qty Updated !" })

        } catch (error) {
            console.log(error.message)
        }

    }// End Method

    add_wishlist = async (req, res) => {
        const { slug } = req.body
        try {
            const product = await WishlistModel.findOne({ slug })
            if (product) {
                responseReturn(res, 404, {
                    error: 'Product Is Already In Wishlist'
                })
            } else {
                await WishlistModel.create(req.body)
                responseReturn(res, 201, {
                    message: 'Product Add to Wishlist Success'
                })
            }
        } catch (error) {
            console.log(error.message)
        }

    }// End Method 

    get_wishlist = async (req, res) => {
        const { userId } = req.params
        try {
            const wishlists = await WishlistModel.find({
                userId
            })
            responseReturn(res, 200, {
                wishlistCount: wishlists.length,
                wishlists
            })

        } catch (error) {
            console.log(error.message)
        }
    }// End Method 

    remove_wishlist = async (req, res) => {
        const { wishlistId } = req.params
        try {
            const wishlist = await WishlistModel.findByIdAndDelete(wishlistId)
            responseReturn(res, 200, {
                message: 'Wishlist Product Remove',
                wishlistId
            })

        } catch (error) {
            console.log(error.message)
        }
    }// End Method 
}

module.exports = new cartController()