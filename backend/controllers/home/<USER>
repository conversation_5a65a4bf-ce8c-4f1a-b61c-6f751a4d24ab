const CustomerModel = require('../../models/customerModel')
const { responseReturn } = require('../../utils/response')
const bcrypt = require('bcrypt')
const SellerCustomerModel = require('../../models/chat/sellerCustomerModel')
const { createToken } = require('../../utils/tokenCreate')

class customerAuthController {
    customer_register = async (req, res) => {
        const { name, email, password } = req.body

        try {
            const customer = await CustomerModel.findOne({ email })
            if (customer) {
                responseReturn(res, 404, { error: 'Email Already Exits !' })
            } else {
                const createCustomer = await CustomerModel.create({
                    name: name.trim(),
                    email: email.trim(),
                    password: await bcrypt.hash(password, 10),
                    method: 'menualy'
                })
                await SellerCustomerModel.create({
                    myId: createCustomer.id
                })
                const token = await createToken({
                    id: createCustomer.id,
                    name: createCustomer.name,
                    email: createCustomer.email,
                    method: createCustomer.method
                })
                res.cookie('customerToken', token, {
                    expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
                })
                responseReturn(res, 201, { message: "User Register Success !", token })
            }
        } catch (error) {
            console.log(error.message)
        }
    }
    // End Method

    customer_login = async (req, res) => {
        const { email, password } = req.body
        try {
            const customer = await CustomerModel.findOne({ email }).select('+password')
            if (customer) {
                const match = await bcrypt.compare(password, customer.password)
                if (match) {
                    const token = await createToken({
                        id: customer.id,
                        name: customer.name,
                        email: customer.email,
                        method: customer.method
                    })
                    res.cookie('customerToken', token, {
                        expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
                    })
                    responseReturn(res, 201, { message: 'User Login Success !', token })

                } else {
                    responseReturn(res, 404, { error: 'Password Wrong !' })
                }
            } else {
                responseReturn(res, 404, { error: 'Email Not Found !' })
            }

        } catch (error) {
            console.log(error.message)
        }
    }
    // End Method

    /// Change Password User
    customer_change_password = async (req, res) => {
        const { email, old_password, new_password, confirm_password } = req.body;
        // 🛡️ Validate input fields
        if (!old_password || !new_password || !confirm_password) {
            return res.status(400).json({ message: 'All fields are required' });
        }
        // 🛡️ Check if new_password and confirm_password match
        if (new_password !== confirm_password) {
            return res.status(400).json({ message: 'Input Password do not match' });
        }
        try {
            // 🔎 Find user by email (make sure password is selected)
            const user = await CustomerModel.findOne({ email }).select('+password');
            if (!user) {
                return res.status(404).json({ message: 'User not found' });
            }
            // 🔒 Compare old password
            const isMatch = await bcrypt.compare(old_password, user.password);
            if (!isMatch) {
                return res.status(400).json({ message: 'Incorrect old password' });
            }
            // ✏️ Hash new password and update
            user.password = await bcrypt.hash(new_password, 10);
            await user.save();
            // ✅ Respond success
            res.json({ message: 'Password changed successfully' });
        } catch (error) {
            console.error('Change password error:', error);
            res.status(500).json({ message: 'Server Error' });
        }
    } // End Method


    customer_logout = async (req, res) => {
        res.cookie('customerToken', "", {
            expires: new Date(Date.now())
        })
        responseReturn(res, 200, { message: 'Logout Success !' })
    }
    // End Method
}

module.exports = new customerAuthController()