// Import required modules
const express = require('express'); // Import the Express framework
const app = express(); // Create an instance of Express

const cors = require('cors'); // Import CORS to handle cross-origin requests
require('dotenv').config(); // Load environment variables from a .env file

const bodyParser = require('body-parser'); // Import body-parser for parsing JSON payloads
const cookieParser = require('cookie-parser'); // Import cookie-parser to handle cookies
const { dbConnect } = require('./utils/db'); // Import the database connection function

// socker io for chat
const socket = require('socket.io')
const http = require('http')
const server = http.createServer(app)

// CORS configuration based on environment
const isDevelopment = process.env.NODE_ENV !== 'production';

const allowedOrigins = isDevelopment
    ? ['http://localhost:3000', 'http://localhost:3001']
    : [process.env.client_customer_production_url, 
       process.env.client_admin_production_url];

// Enable CORS for specified origins
app.use(
    cors({
        origin: function (origin, callback) {
            if (!origin || allowedOrigins.includes(origin)) {
                callback(null, true);
            } else {
                callback(new Error('Not allowed by CORS'));
            }
        },
        credentials: true,
        exposedHeaders: ['set-cookie']
    })
);

// socket io
const io = socket(server, {
    cors: {
        origin: function (origin, callback) {
            if (!origin || allowedOrigins.includes(origin)) {
                callback(null, true);
            } else {
                callback(new Error('Not allowed by CORS'));
            }
        },
        credentials: true
    }
});

var allCustomer = []
var allSeller = []
let admin = {}

// functions for socket
const addUser = (customerId, socketId, userInfo) => {
    const checkUser = allCustomer.some(u => u.customerId === customerId)
    if (!checkUser) {
        allCustomer.push({
            customerId,
            socketId,
            userInfo
        })
    }
}

const addSeller = (sellerId, socketId, userInfo) => {
    const checkSeller = allSeller.some(u => u.sellerId === sellerId)
    if (!checkSeller) {
        allSeller.push({
            sellerId,
            socketId,
            userInfo
        })
    }
}

const findCustomer = (customerId) => {
    return allCustomer.find(c => c.customerId === customerId)
}
const findSeller = (sellerId) => {
    return allSeller.find(c => c.sellerId === sellerId)
}

const remove = (socketId) => {
    allCustomer = allCustomer.filter(c => c.socketId !== socketId)
    allSeller = allSeller.filter(c => c.socketId !== socketId)
}

// Start the socket server
io.on('connection', (soc) => {
    console.log('socket server running...')

    soc.on('add_user', (customerId, userInfo) => {
        // console.log(userInfo)
        addUser(customerId, soc.id, userInfo)
        io.emit('activeSeller', allSeller)
    })

    soc.on('add_seller', (sellerId, userInfo) => {
        addSeller(sellerId, soc.id, userInfo)
        io.emit('activeSeller', allSeller)
    })

    soc.on('send_seller_message', (msg) => {
        const customer = findCustomer(msg.receverId)
        if (customer !== undefined) {
            soc.to(customer.socketId).emit('seller_message', msg)
        }
    })
    soc.on('send_customer_message', (msg) => {
        const seller = findSeller(msg.receverId)
        if (seller !== undefined) {
            soc.to(seller.socketId).emit('customer_message', msg)
        }
    })

    soc.on('send_message_admin_to_seller', (msg) => {
        const seller = findSeller(msg.receverId)
        if (seller !== undefined) {
            soc.to(seller.socketId).emit('receved_admin_message', msg)
        }
    })

    soc.on('send_message_seller_to_admin', (msg) => {
        if (admin.socketId) {
            soc.to(admin.socketId).emit('receved_seller_message', msg)
        }
    })

    soc.on('add_admin', (adminInfo) => {
        delete adminInfo.email
        delete adminInfo.password
        admin = adminInfo
        admin.socketId = soc.id
        io.emit('activeSeller', allSeller)

    })

    soc.on('disconnect', () => {
        console.log('user disconnect')
        remove(soc.id)
        io.emit('activeSeller', allSeller)
    })
})

// Use body-parser middleware to parse JSON request bodies
app.use(bodyParser.json());

// Use cookie-parser middleware to parse cookies in requests
app.use(cookieParser());

// Set up API routes for authentication
app.use('/api/home', require('./routes/home/<USER>'))
app.use('/api', require('./routes/authRoutes'));
app.use('/api', require('./routes/dashboard/categoryRoutes'))
app.use('/api', require('./routes/dashboard/productRoutes'))
app.use('/api', require('./routes/dashboard/sellerRoutes'))
app.use('/api', require('./routes/home/<USER>'))
app.use('/api', require('./routes/home/<USER>'))
app.use('/api', require('./routes/order/orderRoutes'))
app.use('/api', require('./routes/chatRoutes'))
app.use('/api', require('./routes/paymentRoutes'))
app.use('/api', require('./routes/dashboard/dashboardRoutes'))

// Define a basic test route to test the server
app.get('/', (req, res) => res.send('Hello Server-backend by Nodejs'));

// Get the port number from the environment variables or default to 5000
const port = process.env.PORT || 5000;

// Connect to the database
dbConnect();

// Start the server and listen on the specified port
server.listen(port, () => console.log(`Server is running on port ${port}`));
