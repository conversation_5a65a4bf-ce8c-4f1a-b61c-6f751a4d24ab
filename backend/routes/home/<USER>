const customerAuthController = require('../../controllers/home/<USER>')
const router = require('express').Router()

router.post('/customer/customer-register', customerAuthController.customer_register)
router.post('/customer/customer-login', customerAuthController.customer_login)
router.post('/customer/google-login', customerAuthController.google_login)
router.post('/customer-change-password', customerAuthController.customer_change_password)

router.get('/customer/logout', customerAuthController.customer_logout)

module.exports = router 