#!/bin/bash

echo "🔧 Fixing React compatibility issues for deployment..."

# Navigate to frontend directory
cd frontend

echo "📦 Removing node_modules and package-lock.json..."
rm -rf node_modules
rm -f package-lock.json

echo "🧹 Clearing npm cache..."
npm cache clean --force

echo "📦 Installing exact dependency versions..."
npm install

echo "🔨 Testing build..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful! Ready for deployment."
    echo ""
    echo "🚀 Next steps:"
    echo "1. Add REACT_APP_GOOGLE_CLIENT_ID to Vercel environment variables"
    echo "2. git add . && git commit -m 'fix: resolve React compatibility issues'"
    echo "3. git push origin main"
else
    echo "❌ Build failed. Check the error messages above."
fi
