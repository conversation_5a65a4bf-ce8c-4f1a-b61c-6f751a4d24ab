#!/bin/bash

echo "🔧 Fixing React 19 compatibility issues for deployment..."

# Navigate to frontend directory
cd frontend

echo "📦 Removing node_modules and package-lock.json..."
rm -rf node_modules
rm -f package-lock.json

echo "🧹 Clearing npm cache..."
npm cache clean --force

echo "📦 Installing with legacy peer deps to force React 18..."
npm install --legacy-peer-deps

echo "🔨 Testing build..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful! Ready for deployment."
    echo ""
    echo "🚀 Next steps:"
    echo "1. Add REACT_APP_GOOGLE_CLIENT_ID to Vercel environment variables"
    echo "2. git add . && git commit -m 'fix: force React 18 compatibility with overrides'"
    echo "3. git push origin main"
    echo ""
    echo "📋 Changes made:"
    echo "- Added package overrides to force React 18.3.1"
    echo "- Downgraded react-leaflet to compatible version"
    echo "- Added .npmrc with legacy-peer-deps=true"
    echo "- Added missing babel plugin"
else
    echo "❌ Build failed. Check the error messages above."
    echo ""
    echo "🔧 If still failing, try:"
    echo "npm install --force"
    echo "npm run build"
fi
