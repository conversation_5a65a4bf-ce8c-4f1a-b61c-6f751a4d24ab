# 🛒 Multi-Vendor Ecommerce Platform

A full-stack multi-vendor ecommerce platform built with React, Node.js, Express, MongoDB, and Socket.IO.

## 🏗️ Architecture

### **Backend** (Node.js/Express)
- **Location**: `backend/`
- **Deployment**: Render
- **URL**: https://backend-multi-vendors-ecommerce.onrender.com
- **Features**: REST API, Socket.IO, Authentication, Payment Processing

### **Frontend** (React - Customer App)
- **Location**: `frontend/`
- **Deployment**: Vercel
- **URL**: https://frontend-multi-vendors-ercommerce.vercel.app
- **Features**: Product browsing, Shopping cart, Payments, Chat

### **Dashboard** (React - Admin/Seller App)
- **Location**: `dashboard/`
- **Deployment**: Vercel
- **URL**: https://dashboard-multi-vendors-ecommerce.vercel.app
- **Features**: Admin panel, Seller management, Analytics

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB Atlas account
- Cloudinary account
- Stripe account

### Local Development

#### 1. Backend Setup
```bash
cd backend
npm install
cp .env.example .env  # Configure your environment variables
npm run dev
```

#### 2. Frontend Setup
```bash
cd frontend
npm install
npm start
```

#### 3. Dashboard Setup
```bash
cd dashboard
npm install
npm start
```

## 🔧 Environment Variables

### Backend (.env)
```env
NODE_ENV=development
PORT=9000
DB_LOCAL_URL=your_mongodb_connection_string
SECRET=your_jwt_secret
cloud_name=your_cloudinary_name
api_key=your_cloudinary_key
api_secret=your_cloudinary_secret
```

### Frontend (.env.development)
```env
REACT_APP_API_URL=http://localhost:9000/api
REACT_APP_SOCKET_URL=http://localhost:9000
REACT_APP_STRIPE_PUBLISHABLE_KEY=your_stripe_test_key
REACT_APP_FRONTEND_URL=http://localhost:3000
```

### Dashboard (.env.development)
```env
REACT_APP_API_URL=http://localhost:9000/api
REACT_APP_SOCKET_URL=http://localhost:9000
```

## 📁 Project Structure

```
ecommerce-new/
├── backend/                 # Node.js API server
│   ├── controllers/         # Route controllers
│   ├── models/             # MongoDB models
│   ├── routes/             # API routes
│   ├── middlewares/        # Custom middlewares
│   ├── utils/              # Utility functions
│   └── server.js           # Entry point
├── frontend/               # React customer app
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── store/          # Redux store
│   │   └── utils/          # Utility functions
│   └── public/             # Static assets
├── dashboard/              # React admin/seller app
│   ├── src/
│   │   ├── views/          # Page views
│   │   ├── layout/         # Layout components
│   │   ├── store/          # Redux store
│   │   └── utils/          # Utility functions
│   └── public/             # Static assets
└── docs/                   # Documentation
```

## 🔄 Deployment & Updates

### Auto-Deployment (Recommended)
All services are connected to Git and auto-deploy on push:

```bash
git add .
git commit -m "Your changes"
git push origin main
```

### Manual Updates
See `docs/` folder for detailed deployment guides.

## 🛠️ Key Features

### Customer Features
- ✅ User registration/authentication
- ✅ Product browsing and search
- ✅ Shopping cart management
- ✅ Stripe payment integration
- ✅ Real-time chat with sellers
- ✅ Order tracking
- ✅ Wishlist functionality

### Seller Features
- ✅ Seller dashboard
- ✅ Product management
- ✅ Order management
- ✅ Real-time chat with customers
- ✅ Sales analytics
- ✅ Payment management

### Admin Features
- ✅ User management
- ✅ Seller approval
- ✅ Category management
- ✅ Order oversight
- ✅ Platform analytics

## 🔧 Technologies Used

### Backend
- Node.js & Express.js
- MongoDB & Mongoose
- Socket.IO (real-time chat)
- JWT Authentication
- Stripe (payments)
- Cloudinary (image storage)
- bcrypt (password hashing)

### Frontend & Dashboard
- React 18
- Redux Toolkit
- React Router
- Tailwind CSS
- Axios (HTTP client)
- Socket.IO Client
- React Hot Toast

## 📚 Documentation

- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md)
- [Git Update Workflow](docs/GIT_UPDATE_WORKFLOW.md)
- [Update Deployment Guide](docs/UPDATE_DEPLOYMENT_GUIDE.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation in `docs/`
- Review the codebase structure
- Test locally before deploying

---

**Happy coding! 🚀**
