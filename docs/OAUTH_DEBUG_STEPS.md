# 🔧 OAuth Debug Steps

## 🚨 "Access blocked: This app's request is invalid" Fix

This error typically occurs due to:
1. **Incorrect redirect URI** in Google Console
2. **Missing required OAuth parameters**
3. **Client ID mismatch**

## ✅ Step-by-Step Fix

### **Step 1: Verify Google Console Settings**

Go to: https://console.cloud.google.com/apis/credentials

Find OAuth Client ID: `************-okdlhh4biq6f7sg8ecflge493gv99930`

**Authorized JavaScript origins:**
```
http://localhost:3000
https://frontend-multi-vendors-ecommerce.vercel.app
```

**Authorized redirect URIs:**
```
http://localhost:3000/auth/google/callback
https://frontend-multi-vendors-ecommerce.vercel.app/auth/google/callback
```

### **Step 2: Test OAuth URL Manually**

Open this URL in browser (replace YOUR_CLIENT_ID):

```
https://accounts.google.com/o/oauth2/v2/auth?client_id=************-okdlhh4biq6f7sg8ecflge493gv99930.apps.googleusercontent.com&redirect_uri=http://localhost:3000/auth/google/callback&response_type=code&scope=https://www.googleapis.com/auth/userinfo.email%20https://www.googleapis.com/auth/userinfo.profile%20openid&access_type=online&prompt=select_account&include_granted_scopes=true&state=login
```

**Expected:** Should show Google account selection
**If error:** Check redirect URI matches exactly

### **Step 3: Debug Console Logs**

Check browser console for:
```
Redirecting to: https://accounts.google.com/o/oauth2/v2/auth?...
Redirect URI: http://localhost:3000/auth/google/callback
```

### **Step 4: Test Backend Endpoint**

Test the backend OAuth endpoint:
```bash
curl -X POST http://localhost:9000/api/customer/google-oauth-callback \
  -H "Content-Type: application/json" \
  -d '{"code":"test_code"}'
```

## 🔧 Quick Fixes

### **Fix 1: Exact URI Match**
Make sure redirect URI in Google Console matches exactly:
- ✅ `http://localhost:3000/auth/google/callback`
- ❌ `http://localhost:3000/auth/google/callback/`
- ❌ `http://localhost:3000/auth/google/callback?`

### **Fix 2: Enable APIs**
In Google Console, enable these APIs:
- ✅ Google+ API
- ✅ Google Identity Services API
- ✅ People API (optional)

### **Fix 3: OAuth Consent Screen**
Make sure OAuth consent screen is configured:
- ✅ App name: "Easy Shopping Center"
- ✅ User support email
- ✅ Developer contact email
- ✅ Scopes: email, profile, openid

## 🧪 Test Flow

### **Manual Test:**
1. **Go to:** http://localhost:3000/login
2. **Open browser console**
3. **Click "Continue with Google"**
4. **Check console logs**
5. **Should redirect to Google**
6. **Select account**
7. **Should redirect back to callback**

### **Expected Console Logs:**
```
Redirecting to: https://accounts.google.com/o/oauth2/v2/auth?...
Redirect URI: http://localhost:3000/auth/google/callback
Processing OAuth callback with code: 4/0AX4XfWh...
Backend OAuth response: {message: "Google Login Success!", token: "..."}
```

## 🔍 Common Issues

### **Issue 1: "redirect_uri_mismatch"**
**Fix:** Add exact callback URL to Google Console

### **Issue 2: "invalid_client"**
**Fix:** Check Client ID is correct in .env file

### **Issue 3: "access_denied"**
**Fix:** User cancelled - normal behavior

### **Issue 4: "invalid_request"**
**Fix:** Check OAuth parameters are correct

## 🚀 Working Configuration

### **Environment Variables:**
```env
REACT_APP_GOOGLE_CLIENT_ID=************-okdlhh4biq6f7sg8ecflge493gv99930.apps.googleusercontent.com
```

### **OAuth Parameters:**
```javascript
{
  client_id: "************-okdlhh4biq6f7sg8ecflge493gv99930.apps.googleusercontent.com",
  redirect_uri: "http://localhost:3000/auth/google/callback",
  response_type: "code",
  scope: "https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile openid",
  access_type: "online",
  prompt: "select_account",
  include_granted_scopes: "true",
  state: "login"
}
```

### **Backend Processing:**
- ✅ Exchange code for token
- ✅ Get user info from Google
- ✅ Create/update customer record
- ✅ Generate JWT token
- ✅ Set cookie and return token

## 📞 If Still Not Working

1. **Double-check Google Console** redirect URIs
2. **Wait 5-10 minutes** for Google changes to propagate
3. **Try incognito mode** to avoid cache
4. **Check network tab** for failed requests
5. **Verify Client ID** matches exactly

The OAuth flow should work once the redirect URIs are properly configured in Google Console! 🎉
