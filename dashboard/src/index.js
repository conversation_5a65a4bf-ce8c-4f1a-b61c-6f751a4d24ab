import React, { lazy, Suspense } from 'react';
import ReactD<PERSON> from 'react-dom/client';
import './index.css';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import store from './store/index';
import { Toaster } from 'react-hot-toast';

const App = lazy(() => import('./App'));

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <BrowserRouter>
    <Provider store={store} >
      <Suspense>
        <App />
        <Toaster
          toastOptions={{
            duration: 2000,
            position: 'top-right',
            style: {
              // background: '#283046',
              // color: 'white'
              background: '#363636',
              color: '#fff',
            }
          }}
        />
      </Suspense>
    </Provider>
  </BrowserRouter>
);

// Performance monitoring removed for production build
