import io from 'socket.io-client';

export const overrideStyle = {
    display: 'flex',
    margin: '0 auto',
    height: '24px',
    justifyContent: 'center',
    alignItems: 'center'
};

// Environment-aware socket URL
const getSocketUrl = () => {
    if (process.env.REACT_APP_SOCKET_URL) {
        return process.env.REACT_APP_SOCKET_URL;
    }
    return process.env.NODE_ENV === 'production'
        ? 'https://backend-multi-vendors-ecommerce.onrender.com'
        : 'http://localhost:9000';
};

// Socket connection options
const socketOptions = {
    transports: ['websocket', 'polling'],
    timeout: 20000,
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionAttempts: 5,
};

export const socket = io(getSocketUrl(), socketOptions);

// Add connection event listeners for debugging
socket.on('connect', () => {
    console.log('Dashboard socket connected:', socket.id);
});

socket.on('disconnect', (reason) => {
    console.log('Dashboard socket disconnected:', reason);
});

socket.on('connect_error', (error) => {
    console.error('Dashboard socket connection error:', error);
});
