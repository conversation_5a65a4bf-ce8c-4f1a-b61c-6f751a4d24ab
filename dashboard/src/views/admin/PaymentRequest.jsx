import React, { forwardRef, useEffect, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import { confirm_payment_request, get_payment_request, messageClear } from '../../store/Reducers/PaymentReducer';
import moment from 'moment';
import toast from 'react-hot-toast';
import { useDispatch, useSelector } from 'react-redux';

// Function to handle the `onWheel` event, triggered when the user scrolls with the mouse wheel.
// Logs the deltaY value, which indicates the scroll direction and speed.
function handleOnWheel({ deltaY }) {
    console.log('handleOnWheel', deltaY);
}

// A custom React component created using forwardRef to pass a ref down to the `div` element.
// This component also attaches the `handleOnWheel` event handler to the `onWheel` event.
// It spreads the remaining props onto the `div`, ensuring flexibility for other attributes.
const outerElementType = forwardRef((props, ref) => (
    <div ref={ref} onWheel={handleOnWheel} {...props} />
));

const PaymentRequest = () => {

    const dispatch = useDispatch()
    const { successMessage, errorMessage, pendingWithdraws, loader } = useSelector(state => state.payment)
    const [paymentId, setPaymentId] = useState('')

    useEffect(() => {
        dispatch(get_payment_request())
    }, [])

    const confirm_request = (id) => {
        setPaymentId(id)
        dispatch(confirm_payment_request(id))
    }

    useEffect(() => {
        if (successMessage) {
            toast.success(successMessage)
            dispatch(messageClear())
        }
        if (errorMessage) {
            toast.error(errorMessage)
            dispatch(messageClear())
        }
    }, [successMessage, errorMessage])

    const Row = ({ index, style }) => {
        return (
            <div style={style} className='flex text-sm text-white font-medium'>
                <div className='w-[25%] p-2 whitespace-nowrap'>{index + 1}</div>
                <div className='w-[25%] p-2 whitespace-nowrap'>${pendingWithdraws[index]?.amount}</div>
                <div className='w-[25%] p-2 whitespace-nowrap'>
                    <span className={`py-[1px] px-[5px] rounded-md text-sm font-medium capitalize ${
                        pendingWithdraws[index]?.status === 'success'
                            ? 'bg-green-100 text-green-800'
                            : pendingWithdraws[index]?.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                    }`}>
                        {pendingWithdraws[index]?.status}
                    </span>
                </div>
                <div className='w-[25%] p-2 whitespace-nowrap'>{moment(pendingWithdraws[index]?.createdAt).format('LL')}</div>
                <div className='w-[25%] p-2 whitespace-nowrap'>
                    <button disabled={loader} onClick={() => confirm_request(pendingWithdraws[index]?._id)}
                        className='bg-indigo-500 shadow-lg hover:shadow-indigo-500/50 px-3 py-[2px cursor-pointer text-white 
                    rounded-sm text-sm]'>{(loader && paymentId === pendingWithdraws[index]?._id) ? 'loading..' : 'Confirm'}</button>
                </div>
            </div>
        )
    }
    return (
        <div className='px-2 lg:px-7 pt-5'>
            <h1 className='text-[20px] font-bold mb-3'>Payment Request</h1>
            <div className='w-full p-4 bg-[#6a5fdf] rounded-md'>
                <h2 className='text-xl font-medium pb-5 text-[#d0d2d6]'>Withdrawal Request</h2>
                <div className='w-full'>
                    <div className='w-full overflow-x-auto'>
                        <div className='flex bg-[#a7a3de] uppercase text-xs font-bold min-w-[340px] rounded-md'>
                            <div className='w-[25%] p-2'>No</div>
                            <div className='w-[25%] p-2'>Amount</div>
                            <div className='w-[25%] p-2'>Status</div>
                            <div className='w-[25%] p-2'>Date</div>
                            <div className='w-[25%] p-2'>Action</div>
                        </div>
                        {
                            <List
                                style={{ minWidth: '340px' }}
                                className='List'
                                height={350}
                                itemCount={pendingWithdraws.length}
                                itemSize={35}
                                outerElementType={outerElementType}
                            >
                                {Row}
                            </List>
                        }
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PaymentRequest;