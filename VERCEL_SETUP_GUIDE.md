# 🚀 Vercel Environment Setup for Google OAuth

## 📋 Your Google Client ID
```
************-okdlhh4biq6f7sg8ecflge493gv99930.apps.googleusercontent.com
```

## 🔧 Vercel Dashboard Setup

### **Step 1: Access Vercel Project Settings**
1. Go to: https://vercel.com/dashboard
2. Select your project: `frontend-multi-vendors-ecommerce`
3. Click on **Settings** tab
4. Navigate to **Environment Variables** section

### **Step 2: Add Environment Variable**
Click **Add New** and enter:

**Name:**
```
REACT_APP_GOOGLE_CLIENT_ID
```

**Value:**
```
************-okdlhh4biq6f7sg8ecflge493gv99930.apps.googleusercontent.com
```

**Environment:** Select all three:
- ✅ Production
- ✅ Preview  
- ✅ Development

### **Step 3: Redeploy**
After adding the environment variable:
1. Go to **Deployments** tab
2. Click **Redeploy** on the latest deployment
3. Or push a new commit to trigger automatic deployment

---

## 🔧 Google Console Configuration

### **Required Authorized Origins**
Add these to your Google Console OAuth settings:

```
http://localhost:3000
http://localhost:3001
https://frontend-multi-vendors-ecommerce.vercel.app
```

### **Required Redirect URIs**
```
http://localhost:3000
http://localhost:3001  
https://frontend-multi-vendors-ecommerce.vercel.app
```

### **How to Update Google Console:**
1. Visit: https://console.cloud.google.com/
2. Go to: **APIs & Services > Credentials**
3. Find OAuth 2.0 Client ID: `************-okdlhh4biq6f7sg8ecflge493gv99930`
4. Click **Edit** (pencil icon)
5. Add the URLs above to both sections
6. Click **Save**

---

## 🧪 Testing Steps

### **Local Testing:**
1. **Start development server:**
   ```bash
   cd frontend
   npm start
   ```
2. **Go to:** http://localhost:3000/login
3. **Click:** "Continue with Google"
4. **Verify:** Google popup appears and works

### **Production Testing:**
1. **Visit:** https://frontend-multi-vendors-ecommerce.vercel.app/login
2. **Click:** "Continue with Google"  
3. **Verify:** Authentication works on live site

---

## 🔍 Troubleshooting

### **Common Issues:**

#### **"Google Sign-In not available"**
- ✅ Check environment variable is set in Vercel
- ✅ Redeploy after adding environment variable
- ✅ Verify Google Script loads in browser console

#### **"Unauthorized JavaScript origin"**
- ✅ Add your domain to Google Console authorized origins
- ✅ Make sure URLs match exactly (no trailing slashes)
- ✅ Wait a few minutes for Google changes to propagate

#### **"Invalid client ID"**
- ✅ Double-check the Client ID in environment variable
- ✅ Ensure no extra spaces or characters
- ✅ Verify the Client ID exists in Google Console

### **Debug Steps:**
1. **Check browser console** for error messages
2. **Verify environment variable** in Vercel dashboard
3. **Test in incognito mode** to avoid cache issues
4. **Check Network tab** for failed requests

---

## 📱 Expected Behavior

### **Successful Flow:**
1. **User clicks "Continue with Google"**
2. **Google popup window opens**
3. **User selects Google account**
4. **Popup closes automatically**
5. **User is logged in and redirected**
6. **Profile shows Google avatar and info**

### **Error Handling:**
- **Network errors:** Graceful error messages
- **User cancellation:** No error, just return to login
- **Invalid credentials:** Clear error notification
- **API failures:** Retry options provided

---

## 🎯 Quick Checklist

Before testing, ensure:

- ✅ **Environment variable added** to Vercel
- ✅ **Domains added** to Google Console
- ✅ **Latest deployment** includes the changes
- ✅ **Google Script** loads in browser
- ✅ **HTTPS enabled** for production (Vercel default)

---

## 🚀 Ready to Deploy!

Your Google OAuth is now configured with:
- **Client ID:** `************-okdlhh4biq6f7sg8ecflge493gv99930.apps.googleusercontent.com`
- **Environment:** Set up for all Vercel environments
- **Domains:** Ready for both development and production

Just add the environment variable to Vercel and update Google Console with your domains! 🎉
